@use "../utilities/css-variables" as cv;
@use "../utilities/derived-variables" as dv;
@use "../utilities/initial-variables" as iv;
@use "../utilities/extends";
@use "../utilities/mixins" as mx;

$table-color: cv.getVar("text-strong") !default;
$table-background-color: cv.getVar("scheme-main") !default;

$table-cell-border-color: cv.getVar("border") !default;
$table-cell-border-style: solid !default;
$table-cell-border-width: 0 0 1px !default;
$table-cell-padding: 0.5em 0.75em !default;
$table-cell-heading-color: cv.getVar("text-strong") !default;
$table-cell-text-align: left !default;

$table-head-cell-border-width: 0 0 2px !default;
$table-head-cell-color: cv.getVar("text-strong") !default;
$table-foot-cell-border-width: 2px 0 0 !default;
$table-foot-cell-color: cv.getVar("text-strong") !default;

$table-head-background-color: transparent !default;
$table-body-background-color: transparent !default;
$table-foot-background-color: transparent !default;

$table-row-hover-background-color: cv.getVar("scheme-main-bis") !default;

$table-row-active-background-color: cv.getVar("primary") !default;
$table-row-active-color: cv.getVar("primary-invert") !default;

$table-striped-row-even-background-color: cv.getVar("scheme-main-bis") !default;
$table-striped-row-even-hover-background-color: cv.getVar(
  "scheme-main-ter"
) !default;

$table-colors: dv.$colors !default;

.#{iv.$class-prefix}table {
  @include cv.register-vars(
    (
      "table-color": #{$table-color},
      "table-background-color": #{$table-background-color},
      "table-cell-border-color": #{$table-cell-border-color},
      "table-cell-border-style": #{$table-cell-border-style},
      "table-cell-border-width": #{$table-cell-border-width},
      "table-cell-padding": #{$table-cell-padding},
      "table-cell-heading-color": #{$table-cell-heading-color},
      "table-cell-text-align": #{$table-cell-text-align},
      "table-head-cell-border-width": #{$table-head-cell-border-width},
      "table-head-cell-color": #{$table-head-cell-color},
      "table-foot-cell-border-width": #{$table-foot-cell-border-width},
      "table-foot-cell-color": #{$table-foot-cell-color},
      "table-head-background-color": #{$table-head-background-color},
      "table-body-background-color": #{$table-body-background-color},
      "table-foot-background-color": #{$table-foot-background-color},
      "table-row-hover-background-color": #{$table-row-hover-background-color},
      "table-row-active-background-color": #{$table-row-active-background-color},
      "table-row-active-color": #{$table-row-active-color},
      "table-striped-row-even-background-color": #{$table-striped-row-even-background-color},
      "table-striped-row-even-hover-background-color": #{$table-striped-row-even-hover-background-color},
    )
  );
}

.#{iv.$class-prefix}table {
  @extend %block;

  background-color: cv.getVar("table-background-color");
  color: cv.getVar("table-color");

  td,
  th {
    background-color: cv.getVar("table-cell-background-color");
    border-color: cv.getVar("table-cell-border-color");
    border-style: cv.getVar("table-cell-border-style");
    border-width: cv.getVar("table-cell-border-width");
    color: cv.getVar("table-color");
    padding: cv.getVar("table-cell-padding");
    vertical-align: top;

    // Colors
    @each $name, $pair in $table-colors {
      &.#{iv.$class-prefix}is-#{$name} {
        @include cv.register-vars(
          (
            "table-color": #{cv.getVar($name, "", "-invert")},
            "table-cell-heading-color": #{cv.getVar($name, "", "-invert")},
            "table-cell-background-color": #{cv.getVar($name)},
            "table-cell-border-color": #{cv.getVar($name)},
          )
        );
      }
    }

    // Modifiers
    &.#{iv.$class-prefix}is-narrow {
      white-space: nowrap;
      width: 1%;
    }

    &.#{iv.$class-prefix}is-selected {
      background-color: cv.getVar("table-row-active-background-color");
      color: cv.getVar("table-row-active-color");

      a,
      strong {
        color: currentColor;
      }
    }

    &.#{iv.$class-prefix}is-vcentered {
      vertical-align: middle;
    }
  }

  th {
    color: cv.getVar("table-cell-heading-color");

    &:not([align]) {
      text-align: cv.getVar("table-cell-text-align");
    }
  }

  tr {
    &.#{iv.$class-prefix}is-selected {
      background-color: cv.getVar("table-row-active-background-color");
      color: cv.getVar("table-row-active-color");

      a,
      strong {
        color: currentColor;
      }

      td,
      th {
        border-color: cv.getVar("table-row-active-color");
        color: currentColor;
      }
    }

    @each $name, $pair in $table-colors {
      &.#{iv.$class-prefix}is-#{$name} {
        @include cv.register-vars(
          (
            "table-color": #{cv.getVar($name, "", "-invert")},
            "table-cell-heading-color": #{cv.getVar($name, "", "-invert")},
            "table-cell-background-color": #{cv.getVar($name)},
            "table-cell-border-color": #{cv.getVar($name)},
          )
        );
      }
    }
  }

  thead {
    background-color: cv.getVar("table-head-background-color");

    td,
    th {
      border-width: cv.getVar("table-head-cell-border-width");
      color: cv.getVar("table-head-cell-color");
    }
  }

  tfoot {
    background-color: cv.getVar("table-foot-background-color");

    td,
    th {
      border-width: cv.getVar("table-foot-cell-border-width");
      color: cv.getVar("table-foot-cell-color");
    }
  }

  tbody {
    background-color: cv.getVar("table-body-background-color");

    tr {
      &:last-child {
        td,
        th {
          border-bottom-width: 0;
        }
      }
    }
  }

  // Modifiers
  &.#{iv.$class-prefix}is-bordered {
    td,
    th {
      border-width: 1px;
    }

    tr {
      &:last-child {
        td,
        th {
          border-bottom-width: 1px;
        }
      }
    }
  }

  &.#{iv.$class-prefix}is-fullwidth {
    width: 100%;
  }

  &.#{iv.$class-prefix}is-hoverable {
    tbody {
      tr:not(.#{iv.$class-prefix}is-selected) {
        &:hover {
          background-color: cv.getVar("table-row-hover-background-color");
        }
      }
    }

    &.#{iv.$class-prefix}is-striped {
      tbody {
        tr:not(.#{iv.$class-prefix}is-selected) {
          &:hover {
            background-color: cv.getVar("table-row-hover-background-color");

            &:nth-child(even) {
              background-color: cv.getVar(
                "table-striped-row-even-hover-background-color"
              );
            }
          }
        }
      }
    }
  }

  &.#{iv.$class-prefix}is-narrow {
    td,
    th {
      padding: 0.25em 0.5em;
    }
  }

  &.#{iv.$class-prefix}is-striped {
    tbody {
      tr:not(.#{iv.$class-prefix}is-selected) {
        &:nth-child(even) {
          background-color: cv.getVar(
            "table-striped-row-even-background-color"
          );
        }
      }
    }
  }
}

.#{iv.$class-prefix}table-container {
  @extend %block;
  @include mx.overflow-touch;
  overflow: auto;
  overflow-y: hidden;
  max-width: 100%;
}
