.sidebar {
  width: 250px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #343a40;
  z-index: 1000;
  transition: left 0.3s;
}

.main-content {
  margin-left: 250px;
  transition: margin-left 0.3s;
}

.sidebar.collapsed {
  left: -250px;
}

.main-content.collapsed {
  margin-left: 0;
}

.sidebar a {
  color: white;
  padding: 15px;
  display: block;
  text-decoration: none;
}

.sidebar a:hover {
  background-color: #495057;
}

.toggle-sidebar-btn {
  position: absolute;
  top: 15px;
  right: -40px;
  display: none;
}

@media (max-width: 768px) {
  .toggle-sidebar-btn {
    display: inline;
  }
}