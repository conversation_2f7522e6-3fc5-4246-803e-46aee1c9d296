/* Sidebar */
.sidebar {
    background-color: #1b1b1d;
    height: 100vh;
    width: 250px;
    position: fixed;
    top: 0;
    left: 0;
    overflow: hidden;
    transition: width 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
}

.sidebar.collapsed {
    width: 0;
}

/* Nome da aplicação */
.menu-brand {
    margin-bottom: 20px;
    text-align: center;
}

.menu-brand h1 {
    font-size: 1.5em;
    color: #ffffff;
}

/* Usuário */
.menu-user {
    text-align: center;
    margin-bottom: 30px;
}

.menu-user figure {
    margin: 0 auto 10px auto;
    border-radius: 50%;
    padding: 5px;
    background: #ffffff;
    width: 96px;
    height: 96px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-user p {
    margin: 0;
    font-size: 1.2em;
    color: #ffffff;
}

/* Navegação */
.menu-list {
    width: 100%;
}

.menu-list li {
    list-style: none;
    margin-bottom: 10px;
}

.menu-list a {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: #ffffff;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.menu-list a:hover {
    background-color: #292b2f;
}

.menu-list a.is-active {
    background-color: #4a90e2;
}

.menu-list a .icon {
    margin-right: 10px;
    font-size: 1.2em;
}

/* Botão de Recolher */
#toggle-sidebar {
    position: fixed;
    top: 15px;
    left: 15px;
    background: #292b2f;
    color: #ffffff;
    border: none;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    z-index: 1100;
}

#toggle-sidebar:hover {
    background: #4a90e2;
}
