@use "../utilities/css-variables" as cv;
@use "../utilities/initial-variables" as iv;
@use "../utilities/extends";
@use "../utilities/mixins" as mx;

$media-border-color: hsla(
  #{cv.getVar("scheme-h")},
  #{cv.getVar("scheme-s")},
  #{cv.getVar("border-l")},
  0.5
) !default;
$media-border-size: 1px !default;
$media-spacing: 1rem !default;
$media-spacing-large: 1.5rem !default;
$media-content-spacing: 0.75rem !default;
$media-level-1-spacing: 0.75rem !default;
$media-level-1-content-spacing: 0.5rem !default;
$media-level-2-spacing: 0.5rem !default;

.#{iv.$class-prefix}media {
  @extend %block;
  @include cv.register-vars(
    (
      "media-border-color": #{$media-border-color},
      "media-border-size": #{$media-border-size},
      "media-spacing": #{$media-spacing},
      "media-spacing-large": #{$media-spacing-large},
      "media-content-spacing": #{$media-content-spacing},
      "media-level-1-spacing": #{$media-level-1-spacing},
      "media-level-1-content-spacing": #{$media-level-1-content-spacing},
      "media-level-2-spacing": #{$media-level-2-spacing},
    )
  );
  align-items: flex-start;
  display: flex;
  text-align: inherit;

  .#{iv.$class-prefix}content:not(:last-child) {
    margin-bottom: cv.getVar("media-content-spacing");
  }

  .#{iv.$class-prefix}media {
    border-top-color: cv.getVar("media-border-color");
    border-top-style: solid;
    border-top-width: cv.getVar("media-border-size");
    display: flex;
    padding-top: cv.getVar("media-level-1-spacing");

    .#{iv.$class-prefix}content:not(:last-child),
    .#{iv.$class-prefix}control:not(:last-child) {
      margin-bottom: cv.getVar("media-level-1-content-spacing");
    }

    .#{iv.$class-prefix}media {
      padding-top: cv.getVar("media-level-2-spacing");

      & + .#{iv.$class-prefix}media {
        margin-top: cv.getVar("media-level-2-spacing");
      }
    }
  }

  & + .#{iv.$class-prefix}media {
    border-top-color: cv.getVar("media-border-color");
    border-top-style: solid;
    border-top-width: cv.getVar("media-border-size");
    margin-top: cv.getVar("media-spacing");
    padding-top: cv.getVar("media-spacing");
  }

  // Sizes
  &.#{iv.$class-prefix}is-large {
    & + .#{iv.$class-prefix}media {
      margin-top: cv.getVar("media-spacing-large");
      padding-top: cv.getVar("media-spacing-large");
    }
  }
}

.#{iv.$class-prefix}media-left,
.#{iv.$class-prefix}media-right {
  flex-basis: auto;
  flex-grow: 0;
  flex-shrink: 0;
}

.#{iv.$class-prefix}media-left {
  margin-inline-end: cv.getVar("media-spacing");
}

.#{iv.$class-prefix}media-right {
  margin-inline-start: cv.getVar("media-spacing");
}

.#{iv.$class-prefix}media-content {
  flex-basis: auto;
  flex-grow: 1;
  flex-shrink: 1;
  text-align: inherit;
}

@include mx.mobile {
  .#{iv.$class-prefix}media-content {
    overflow-x: auto;
  }
}
