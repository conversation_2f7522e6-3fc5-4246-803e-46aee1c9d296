@charset "UTF-8";
/*! bulma.io v1.0.2 | MIT License | github.com/jgthms/bulma */
/* Bulma Utilities */
:root {
  --bulma-control-radius: var(--bulma-radius);
  --bulma-control-radius-small: var(--bulma-radius-small);
  --bulma-control-border-width: 1px;
  --bulma-control-height: 2.5em;
  --bulma-control-line-height: 1.5;
  --bulma-control-padding-vertical: calc(0.5em - 1px);
  --bulma-control-padding-horizontal: calc(0.75em - 1px);
  --bulma-control-size: var(--bulma-size-normal);
  --bulma-control-focus-shadow-l: 50%;
}

/* Bulma Base */
/*! minireset.css v0.0.6 | MIT License | github.com/jgthms/minireset.css */
html,
body,
p,
ol,
ul,
li,
dl,
dt,
dd,
blockquote,
figure,
fieldset,
legend,
textarea,
pre,
iframe,
hr,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: normal;
}

ul {
  list-style: none;
}

button,
input,
select,
textarea {
  margin: 0;
}

html {
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: inherit;
}

img,
video {
  height: auto;
  max-width: 100%;
}

iframe {
  border: 0;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}
td:not([align]),
th:not([align]) {
  text-align: inherit;
}

:root {
  --bulma-body-background-color: var(--bulma-scheme-main);
  --bulma-body-size: 1em;
  --bulma-body-min-width: 300px;
  --bulma-body-rendering: optimizeLegibility;
  --bulma-body-family: var(--bulma-family-primary);
  --bulma-body-overflow-x: hidden;
  --bulma-body-overflow-y: scroll;
  --bulma-body-color: var(--bulma-text);
  --bulma-body-font-size: 1em;
  --bulma-body-weight: var(--bulma-weight-normal);
  --bulma-body-line-height: 1.5;
  --bulma-code-family: var(--bulma-family-code);
  --bulma-code-padding: 0.25em 0.5em 0.25em;
  --bulma-code-weight: normal;
  --bulma-code-size: 0.875em;
  --bulma-small-font-size: 0.875em;
  --bulma-hr-background-color: var(--bulma-background);
  --bulma-hr-height: 2px;
  --bulma-hr-margin: 1.5rem 0;
  --bulma-strong-color: var(--bulma-text-strong);
  --bulma-strong-weight: var(--bulma-weight-semibold);
  --bulma-pre-font-size: 0.875em;
  --bulma-pre-padding: 1.25rem 1.5rem;
  --bulma-pre-code-font-size: 1em;
}

html {
  background-color: var(--bulma-body-background-color);
  font-size: var(--bulma-body-size);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  min-width: var(--bulma-body-min-width);
  overflow-x: var(--bulma-body-overflow-x);
  overflow-y: var(--bulma-body-overflow-y);
  text-rendering: var(--bulma-body-rendering);
  text-size-adjust: 100%;
}

article,
aside,
figure,
footer,
header,
hgroup,
section {
  display: block;
}

body,
button,
input,
optgroup,
select,
textarea {
  font-family: var(--bulma-body-family);
}

code,
pre {
  -moz-osx-font-smoothing: auto;
  -webkit-font-smoothing: auto;
  font-family: var(--bulma-code-family);
}

body {
  color: var(--bulma-body-color);
  font-size: var(--bulma-body-font-size);
  font-weight: var(--bulma-body-weight);
  line-height: var(--bulma-body-line-height);
}

a,
button {
  cursor: pointer;
}
a:focus-visible,
button:focus-visible {
  outline-color: hsl(var(--bulma-focus-h), var(--bulma-focus-s), var(--bulma-focus-l));
  outline-offset: var(--bulma-focus-offset);
  outline-style: var(--bulma-focus-style);
  outline-width: var(--bulma-focus-width);
}
a:focus-visible:active,
button:focus-visible:active {
  outline-width: 1px;
}
a:active,
button:active {
  outline-width: 1px;
}

a {
  color: var(--bulma-link-text);
  cursor: pointer;
  text-decoration: none;
  transition-duration: var(--bulma-duration);
  transition-property: background-color, border-color, color;
}
a strong {
  color: currentColor;
}

button {
  appearance: none;
  background: none;
  border: none;
  color: inherit;
  font-family: inherit;
  font-size: 1em;
  margin: 0;
  padding: 0;
  transition-duration: var(--bulma-duration);
  transition-property: background-color, border-color, color;
}

code {
  background-color: var(--bulma-code-background);
  border-radius: 0.5em;
  color: var(--bulma-code);
  font-size: var(--bulma-code-size);
  font-weight: var(--bulma-code-weight);
  padding: var(--bulma-code-padding);
}

hr {
  background-color: var(--bulma-hr-background-color);
  border: none;
  display: block;
  height: var(--bulma-hr-height);
  margin: var(--bulma-hr-margin);
}

img {
  height: auto;
  max-width: 100%;
}

input[type=checkbox],
input[type=radio] {
  vertical-align: baseline;
}

small {
  font-size: var(--bulma-small-font-size);
}

span {
  font-style: inherit;
  font-weight: inherit;
}

strong {
  color: var(--bulma-strong-color);
  font-weight: var(--bulma-strong-weight);
}

svg {
  height: auto;
  width: auto;
}

fieldset {
  border: none;
}

pre {
  -webkit-overflow-scrolling: touch;
  background-color: var(--bulma-pre-background);
  color: var(--bulma-pre);
  font-size: var(--bulma-pre-font-size);
  overflow-x: auto;
  padding: var(--bulma-pre-padding);
  white-space: pre;
  word-wrap: normal;
}
pre code {
  background-color: transparent;
  color: currentColor;
  font-size: var(--bulma-pre-code-font-size);
  padding: 0;
}

table td,
table th {
  vertical-align: top;
}
table td:not([align]),
table th:not([align]) {
  text-align: inherit;
}
table th {
  color: var(--bulma-text-strong);
}

@keyframes spinAround {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
@keyframes pulsate {
  50% {
    opacity: 0.5;
  }
}
/* Bulma Elements */
.bulma-navbar-link:not(.bulma-is-arrowless)::after, .bulma-select:not(.bulma-is-multiple):not(.bulma-is-loading)::after {
  border: 0.125em solid var(--bulma-arrow-color);
  border-right: 0;
  border-top: 0;
  content: " ";
  display: block;
  height: 0.625em;
  margin-top: -0.4375em;
  pointer-events: none;
  position: absolute;
  top: 50%;
  transform: rotate(-45deg);
  transform-origin: center;
  transition-duration: var(--bulma-duration);
  transition-property: border-color;
  width: 0.625em;
}

.bulma-skeleton-block:not(:last-child), .bulma-media:not(:last-child), .bulma-level:not(:last-child), .bulma-fixed-grid:not(:last-child), .bulma-grid:not(:last-child), .bulma-tabs:not(:last-child), .bulma-pagination:not(:last-child), .bulma-message:not(:last-child), .bulma-card:not(:last-child), .bulma-breadcrumb:not(:last-child), .bulma-field:not(:last-child), .bulma-file:not(:last-child), .bulma-title:not(:last-child),
.bulma-subtitle:not(:last-child), .bulma-tags:not(:last-child), .bulma-table:not(:last-child), .bulma-table-container:not(:last-child), .bulma-progress:not(:last-child), .bulma-notification:not(:last-child), .bulma-content:not(:last-child), .bulma-buttons:not(:last-child), .bulma-box:not(:last-child), .bulma-block:not(:last-child) {
  margin-bottom: var(--bulma-block-spacing);
}

.bulma-pagination-previous,
.bulma-pagination-next,
.bulma-pagination-link,
.bulma-pagination-ellipsis, .bulma-file-cta,
.bulma-file-name, .bulma-select select, .bulma-input, .bulma-textarea, .bulma-button {
  align-items: center;
  appearance: none;
  border-color: transparent;
  border-style: solid;
  border-width: var(--bulma-control-border-width);
  border-radius: var(--bulma-control-radius);
  box-shadow: none;
  display: inline-flex;
  font-size: var(--bulma-control-size);
  height: var(--bulma-control-height);
  justify-content: flex-start;
  line-height: var(--bulma-control-line-height);
  padding-bottom: var(--bulma-control-padding-vertical);
  padding-left: var(--bulma-control-padding-horizontal);
  padding-right: var(--bulma-control-padding-horizontal);
  padding-top: var(--bulma-control-padding-vertical);
  position: relative;
  transition-duration: var(--bulma-duration);
  transition-property: background-color, border-color, box-shadow, color;
  vertical-align: top;
}
.bulma-pagination-previous:focus,
.bulma-pagination-next:focus,
.bulma-pagination-link:focus,
.bulma-pagination-ellipsis:focus, .bulma-file-cta:focus,
.bulma-file-name:focus, .bulma-select select:focus, .bulma-input:focus, .bulma-textarea:focus, .bulma-button:focus, .bulma-pagination-previous:focus-visible,
.bulma-pagination-next:focus-visible,
.bulma-pagination-link:focus-visible,
.bulma-pagination-ellipsis:focus-visible, .bulma-file-cta:focus-visible,
.bulma-file-name:focus-visible, .bulma-select select:focus-visible, .bulma-input:focus-visible, .bulma-textarea:focus-visible, .bulma-button:focus-visible, .bulma-pagination-previous:focus-within,
.bulma-pagination-next:focus-within,
.bulma-pagination-link:focus-within,
.bulma-pagination-ellipsis:focus-within, .bulma-file-cta:focus-within,
.bulma-file-name:focus-within, .bulma-select select:focus-within, .bulma-input:focus-within, .bulma-textarea:focus-within, .bulma-button:focus-within, .bulma-is-focused.bulma-pagination-previous,
.bulma-is-focused.bulma-pagination-next,
.bulma-is-focused.bulma-pagination-link,
.bulma-is-focused.bulma-pagination-ellipsis, .bulma-is-focused.bulma-file-cta,
.bulma-is-focused.bulma-file-name, .bulma-select select.bulma-is-focused, .bulma-is-focused.bulma-input, .bulma-is-focused.bulma-textarea, .bulma-is-focused.bulma-button, .bulma-pagination-previous:active,
.bulma-pagination-next:active,
.bulma-pagination-link:active,
.bulma-pagination-ellipsis:active, .bulma-file-cta:active,
.bulma-file-name:active, .bulma-select select:active, .bulma-input:active, .bulma-textarea:active, .bulma-button:active, .bulma-is-active.bulma-pagination-previous,
.bulma-is-active.bulma-pagination-next,
.bulma-is-active.bulma-pagination-link,
.bulma-is-active.bulma-pagination-ellipsis, .bulma-is-active.bulma-file-cta,
.bulma-is-active.bulma-file-name, .bulma-select select.bulma-is-active, .bulma-is-active.bulma-input, .bulma-is-active.bulma-textarea, .bulma-is-active.bulma-button {
  outline: none;
}
[disabled].bulma-pagination-previous,
[disabled].bulma-pagination-next,
[disabled].bulma-pagination-link,
[disabled].bulma-pagination-ellipsis, [disabled].bulma-file-cta,
[disabled].bulma-file-name, .bulma-select select[disabled], [disabled].bulma-input, [disabled].bulma-textarea, [disabled].bulma-button, fieldset[disabled] .bulma-pagination-previous,
fieldset[disabled] .bulma-pagination-next,
fieldset[disabled] .bulma-pagination-link,
fieldset[disabled] .bulma-pagination-ellipsis, fieldset[disabled] .bulma-file-cta,
fieldset[disabled] .bulma-file-name, fieldset[disabled] .bulma-select select, .bulma-select fieldset[disabled] select, fieldset[disabled] .bulma-input, fieldset[disabled] .bulma-textarea, fieldset[disabled] .bulma-button {
  cursor: not-allowed;
}

.bulma-modal-close {
  --bulma-delete-dimensions: 1.25rem;
  --bulma-delete-background-l: 0%;
  --bulma-delete-background-alpha: 0.5;
  --bulma-delete-color: var(--bulma-white);
  appearance: none;
  background-color: hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-delete-background-l), var(--bulma-delete-background-alpha));
  border: none;
  border-radius: var(--bulma-radius-rounded);
  cursor: pointer;
  pointer-events: auto;
  display: inline-flex;
  flex-grow: 0;
  flex-shrink: 0;
  font-size: 1em;
  height: var(--bulma-delete-dimensions);
  max-height: var(--bulma-delete-dimensions);
  max-width: var(--bulma-delete-dimensions);
  min-height: var(--bulma-delete-dimensions);
  min-width: var(--bulma-delete-dimensions);
  outline: none;
  position: relative;
  vertical-align: top;
  width: var(--bulma-delete-dimensions);
}
.bulma-modal-close::before, .bulma-modal-close::after {
  background-color: var(--bulma-delete-color);
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform-origin: center center;
}
.bulma-modal-close::before {
  height: 2px;
  width: 50%;
}
.bulma-modal-close::after {
  height: 50%;
  width: 2px;
}
.bulma-modal-close:hover, .bulma-modal-close:focus {
  --bulma-delete-background-alpha: 0.4;
}
.bulma-modal-close:active {
  --bulma-delete-background-alpha: 0.5;
}
.bulma-is-small.bulma-modal-close {
  --bulma-delete-dimensions: 1rem;
}
.bulma-is-medium.bulma-modal-close {
  --bulma-delete-dimensions: 1.5rem;
}
.bulma-is-large.bulma-modal-close {
  --bulma-delete-dimensions: 2rem;
}

.bulma-control.bulma-is-loading::after, .bulma-select.bulma-is-loading::after, .bulma-button.bulma-is-loading::after {
  animation: spinAround 500ms infinite linear;
  border: 2px solid var(--bulma-loading-color);
  border-radius: var(--bulma-radius-rounded);
  border-right-color: transparent;
  border-top-color: transparent;
  content: "";
  display: block;
  height: 1em;
  position: relative;
  width: 1em;
}

.bulma-hero-video, .bulma-modal, .bulma-modal-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.bulma-navbar-burger, .bulma-menu-list a,
.bulma-menu-list button,
.bulma-menu-list .bulma-menu-item {
  appearance: none;
  background: none;
  border: none;
  color: inherit;
  font-family: inherit;
  font-size: 1em;
  margin: 0;
  padding: 0;
}

.bulma-tabs, .bulma-pagination-previous,
.bulma-pagination-next,
.bulma-pagination-link,
.bulma-pagination-ellipsis, .bulma-breadcrumb, .bulma-file, .bulma-button {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.bulma-box {
  --bulma-box-background-color: var(--bulma-scheme-main);
  --bulma-box-color: var(--bulma-text);
  --bulma-box-radius: var(--bulma-radius-large);
  --bulma-box-shadow: var(--bulma-shadow);
  --bulma-box-padding: 1.25rem;
  --bulma-box-link-hover-shadow: 0 0.5em 1em -0.125em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1), 0 0 0 1px var(--bulma-link);
  --bulma-box-link-active-shadow: inset 0 1px 2px hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.2), 0 0 0 1px var(--bulma-link);
}

.bulma-box {
  background-color: var(--bulma-box-background-color);
  border-radius: var(--bulma-box-radius);
  box-shadow: var(--bulma-box-shadow);
  color: var(--bulma-box-color);
  display: block;
  padding: var(--bulma-box-padding);
}

a.bulma-box:hover, a.bulma-box:focus {
  box-shadow: var(--bulma-box-link-hover-shadow);
}
a.bulma-box:active {
  box-shadow: var(--bulma-box-link-active-shadow);
}

.bulma-button {
  --bulma-button-family: false;
  --bulma-button-weight: var(--bulma-weight-medium);
  --bulma-button-border-color: var(--bulma-border);
  --bulma-button-border-style: solid;
  --bulma-button-border-width: var(--bulma-control-border-width);
  --bulma-button-padding-vertical: 0.5em;
  --bulma-button-padding-horizontal: 1em;
  --bulma-button-focus-border-color: var(--bulma-link-focus-border);
  --bulma-button-focus-box-shadow-size: 0 0 0 0.125em;
  --bulma-button-focus-box-shadow-color: hsla(var(--bulma-link-h), var(--bulma-link-s), var(--bulma-link-on-scheme-l), 0.25);
  --bulma-button-active-color: var(--bulma-link-active);
  --bulma-button-active-border-color: var(--bulma-link-active-border);
  --bulma-button-text-color: var(--bulma-text);
  --bulma-button-text-decoration: underline;
  --bulma-button-text-hover-background-color: var(--bulma-background);
  --bulma-button-text-hover-color: var(--bulma-text-strong);
  --bulma-button-ghost-background: none;
  --bulma-button-ghost-border-color: transparent;
  --bulma-button-ghost-color: var(--bulma-link-text);
  --bulma-button-ghost-decoration: none;
  --bulma-button-ghost-hover-color: var(--bulma-link);
  --bulma-button-ghost-hover-decoration: underline;
  --bulma-button-disabled-background-color: var(--bulma-scheme-main);
  --bulma-button-disabled-border-color: var(--bulma-border);
  --bulma-button-disabled-shadow: none;
  --bulma-button-disabled-opacity: 0.5;
  --bulma-button-static-color: var(--bulma-text-weak);
  --bulma-button-static-background-color: var(--bulma-scheme-main-ter);
  --bulma-button-static-border-color: var(--bulma-border);
}

.bulma-button {
  --bulma-button-h: var(--bulma-scheme-h);
  --bulma-button-s: var(--bulma-scheme-s);
  --bulma-button-l: var(--bulma-scheme-main-l);
  --bulma-button-background-l: var(--bulma-scheme-main-l);
  --bulma-button-background-l-delta: 0%;
  --bulma-button-hover-background-l-delta: var(--bulma-hover-background-l-delta);
  --bulma-button-active-background-l-delta: var(--bulma-active-background-l-delta);
  --bulma-button-color-l: var(--bulma-text-strong-l);
  --bulma-button-border-l: var(--bulma-border-l);
  --bulma-button-border-l-delta: 0%;
  --bulma-button-hover-border-l-delta: var(--bulma-hover-border-l-delta);
  --bulma-button-active-border-l-delta: var(--bulma-active-border-l-delta);
  --bulma-button-focus-border-l-delta: var(--bulma-focus-border-l-delta);
  --bulma-button-outer-shadow-h: 0;
  --bulma-button-outer-shadow-s: 0%;
  --bulma-button-outer-shadow-l: 20%;
  --bulma-button-outer-shadow-a: 0.05;
  --bulma-loading-color: hsl(var(--bulma-button-h), var(--bulma-button-s), var(--bulma-button-color-l));
  background-color: hsl(var(--bulma-button-h), var(--bulma-button-s), calc(var(--bulma-button-background-l) + var(--bulma-button-background-l-delta)));
  border-color: hsl(var(--bulma-button-h), var(--bulma-button-s), calc(var(--bulma-button-border-l) + var(--bulma-button-border-l-delta)));
  border-style: var(--bulma-button-border-style);
  border-width: var(--bulma-button-border-width);
  box-shadow: 0px 0.0625em 0.125em hsla(var(--bulma-button-outer-shadow-h), var(--bulma-button-outer-shadow-s), var(--bulma-button-outer-shadow-l), var(--bulma-button-outer-shadow-a)), 0px 0.125em 0.25em hsla(var(--bulma-button-outer-shadow-h), var(--bulma-button-outer-shadow-s), var(--bulma-button-outer-shadow-l), var(--bulma-button-outer-shadow-a));
  color: hsl(var(--bulma-button-h), var(--bulma-button-s), var(--bulma-button-color-l));
  cursor: pointer;
  font-weight: var(--bulma-button-weight);
  height: auto;
  justify-content: center;
  padding-bottom: calc(var(--bulma-button-padding-vertical) - var(--bulma-button-border-width));
  padding-left: calc(var(--bulma-button-padding-horizontal) - var(--bulma-button-border-width));
  padding-right: calc(var(--bulma-button-padding-horizontal) - var(--bulma-button-border-width));
  padding-top: calc(var(--bulma-button-padding-vertical) - var(--bulma-button-border-width));
  text-align: center;
  white-space: nowrap;
}
.bulma-button strong {
  color: inherit;
}
.bulma-button .bulma-icon, .bulma-button .bulma-icon.bulma-is-small, .bulma-button .bulma-icon.bulma-is-medium, .bulma-button .bulma-icon.bulma-is-large {
  height: 1.5em;
  width: 1.5em;
}
.bulma-button .bulma-icon:first-child:not(:last-child) {
  margin-inline-start: calc(-0.5 * var(--bulma-button-padding-horizontal));
  margin-inline-end: calc(var(--bulma-button-padding-horizontal) * 0.25);
}
.bulma-button .bulma-icon:last-child:not(:first-child) {
  margin-inline-start: calc(var(--bulma-button-padding-horizontal) * 0.25);
  margin-inline-end: calc(-0.5 * var(--bulma-button-padding-horizontal));
}
.bulma-button .bulma-icon:first-child:last-child {
  margin-inline-start: calc(-0.5 * var(--bulma-button-padding-horizontal));
  margin-inline-end: calc(-0.5 * var(--bulma-button-padding-horizontal));
}
.bulma-button:hover, .bulma-button.bulma-is-hovered {
  --bulma-button-background-l-delta: var(--bulma-button-hover-background-l-delta);
  --bulma-button-border-l-delta: var(--bulma-button-hover-border-l-delta);
}
.bulma-button:focus-visible, .bulma-button.bulma-is-focused {
  --bulma-button-border-width: 1px;
  border-color: hsl(var(--bulma-focus-h), var(--bulma-focus-s), var(--bulma-focus-l));
  box-shadow: var(--bulma-focus-shadow-size) hsla(var(--bulma-focus-h), var(--bulma-focus-s), var(--bulma-focus-l), var(--bulma-focus-shadow-alpha));
}
.bulma-button:active, .bulma-button.bulma-is-active {
  --bulma-button-background-l-delta: var(--bulma-button-active-background-l-delta);
  --bulma-button-border-l-delta: var(--bulma-button-active-border-l-delta);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button[disabled], fieldset[disabled] .bulma-button {
  background-color: var(--bulma-button-disabled-background-color);
  border-color: var(--bulma-button-disabled-border-color);
  box-shadow: var(--bulma-button-disabled-shadow);
  opacity: var(--bulma-button-disabled-opacity);
}
.bulma-button.bulma-is-white {
  --bulma-button-h: var(--bulma-white-h);
  --bulma-button-s: var(--bulma-white-s);
  --bulma-button-l: var(--bulma-white-l);
  --bulma-button-background-l: var(--bulma-white-l);
  --bulma-button-border-l: var(--bulma-white-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-white-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-white:focus-visible, .bulma-button.bulma-is-white.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-white.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-white.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-white[disabled], fieldset[disabled] .bulma-button.bulma-is-white {
  background-color: var(--bulma-white);
  border-color: var(--bulma-white);
  box-shadow: none;
}
.bulma-button.bulma-is-black {
  --bulma-button-h: var(--bulma-black-h);
  --bulma-button-s: var(--bulma-black-s);
  --bulma-button-l: var(--bulma-black-l);
  --bulma-button-background-l: var(--bulma-black-l);
  --bulma-button-border-l: var(--bulma-black-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-black-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-black:focus-visible, .bulma-button.bulma-is-black.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-black.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-black.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-black[disabled], fieldset[disabled] .bulma-button.bulma-is-black {
  background-color: var(--bulma-black);
  border-color: var(--bulma-black);
  box-shadow: none;
}
.bulma-button.bulma-is-light {
  --bulma-button-h: var(--bulma-light-h);
  --bulma-button-s: var(--bulma-light-s);
  --bulma-button-l: var(--bulma-light-l);
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-border-l: var(--bulma-light-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-light-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-light:focus-visible, .bulma-button.bulma-is-light.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-light.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-light.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-light[disabled], fieldset[disabled] .bulma-button.bulma-is-light {
  background-color: var(--bulma-light);
  border-color: var(--bulma-light);
  box-shadow: none;
}
.bulma-button.bulma-is-dark {
  --bulma-button-h: var(--bulma-dark-h);
  --bulma-button-s: var(--bulma-dark-s);
  --bulma-button-l: var(--bulma-dark-l);
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-border-l: var(--bulma-dark-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-dark-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-dark:focus-visible, .bulma-button.bulma-is-dark.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-dark.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-dark.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-dark[disabled], fieldset[disabled] .bulma-button.bulma-is-dark {
  background-color: var(--bulma-dark);
  border-color: var(--bulma-dark);
  box-shadow: none;
}
.bulma-button.bulma-is-text {
  --bulma-button-h: var(--bulma-text-h);
  --bulma-button-s: var(--bulma-text-s);
  --bulma-button-l: var(--bulma-text-l);
  --bulma-button-background-l: var(--bulma-text-l);
  --bulma-button-border-l: var(--bulma-text-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-text-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-text:focus-visible, .bulma-button.bulma-is-text.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-text.bulma-is-light {
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-color-l: var(--bulma-text-light-invert-l);
}
.bulma-button.bulma-is-text.bulma-is-dark {
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-color-l: var(--bulma-text-dark-invert-l);
}
.bulma-button.bulma-is-text.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-text.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-text[disabled], fieldset[disabled] .bulma-button.bulma-is-text {
  background-color: var(--bulma-text);
  border-color: var(--bulma-text);
  box-shadow: none;
}
.bulma-button.bulma-is-primary {
  --bulma-button-h: var(--bulma-primary-h);
  --bulma-button-s: var(--bulma-primary-s);
  --bulma-button-l: var(--bulma-primary-l);
  --bulma-button-background-l: var(--bulma-primary-l);
  --bulma-button-border-l: var(--bulma-primary-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-primary-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-primary:focus-visible, .bulma-button.bulma-is-primary.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-primary.bulma-is-light {
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-color-l: var(--bulma-primary-light-invert-l);
}
.bulma-button.bulma-is-primary.bulma-is-dark {
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-color-l: var(--bulma-primary-dark-invert-l);
}
.bulma-button.bulma-is-primary.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-primary.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-primary[disabled], fieldset[disabled] .bulma-button.bulma-is-primary {
  background-color: var(--bulma-primary);
  border-color: var(--bulma-primary);
  box-shadow: none;
}
.bulma-button.bulma-is-link {
  --bulma-button-h: var(--bulma-link-h);
  --bulma-button-s: var(--bulma-link-s);
  --bulma-button-l: var(--bulma-link-l);
  --bulma-button-background-l: var(--bulma-link-l);
  --bulma-button-border-l: var(--bulma-link-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-link-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-link:focus-visible, .bulma-button.bulma-is-link.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-link.bulma-is-light {
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-color-l: var(--bulma-link-light-invert-l);
}
.bulma-button.bulma-is-link.bulma-is-dark {
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-color-l: var(--bulma-link-dark-invert-l);
}
.bulma-button.bulma-is-link.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-link.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-link[disabled], fieldset[disabled] .bulma-button.bulma-is-link {
  background-color: var(--bulma-link);
  border-color: var(--bulma-link);
  box-shadow: none;
}
.bulma-button.bulma-is-info {
  --bulma-button-h: var(--bulma-info-h);
  --bulma-button-s: var(--bulma-info-s);
  --bulma-button-l: var(--bulma-info-l);
  --bulma-button-background-l: var(--bulma-info-l);
  --bulma-button-border-l: var(--bulma-info-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-info-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-info:focus-visible, .bulma-button.bulma-is-info.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-info.bulma-is-light {
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-color-l: var(--bulma-info-light-invert-l);
}
.bulma-button.bulma-is-info.bulma-is-dark {
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-color-l: var(--bulma-info-dark-invert-l);
}
.bulma-button.bulma-is-info.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-info.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-info[disabled], fieldset[disabled] .bulma-button.bulma-is-info {
  background-color: var(--bulma-info);
  border-color: var(--bulma-info);
  box-shadow: none;
}
.bulma-button.bulma-is-success {
  --bulma-button-h: var(--bulma-success-h);
  --bulma-button-s: var(--bulma-success-s);
  --bulma-button-l: var(--bulma-success-l);
  --bulma-button-background-l: var(--bulma-success-l);
  --bulma-button-border-l: var(--bulma-success-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-success-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-success:focus-visible, .bulma-button.bulma-is-success.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-success.bulma-is-light {
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-color-l: var(--bulma-success-light-invert-l);
}
.bulma-button.bulma-is-success.bulma-is-dark {
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-color-l: var(--bulma-success-dark-invert-l);
}
.bulma-button.bulma-is-success.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-success.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-success[disabled], fieldset[disabled] .bulma-button.bulma-is-success {
  background-color: var(--bulma-success);
  border-color: var(--bulma-success);
  box-shadow: none;
}
.bulma-button.bulma-is-warning {
  --bulma-button-h: var(--bulma-warning-h);
  --bulma-button-s: var(--bulma-warning-s);
  --bulma-button-l: var(--bulma-warning-l);
  --bulma-button-background-l: var(--bulma-warning-l);
  --bulma-button-border-l: var(--bulma-warning-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-warning-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-warning:focus-visible, .bulma-button.bulma-is-warning.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-warning.bulma-is-light {
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-color-l: var(--bulma-warning-light-invert-l);
}
.bulma-button.bulma-is-warning.bulma-is-dark {
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-color-l: var(--bulma-warning-dark-invert-l);
}
.bulma-button.bulma-is-warning.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-warning.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-warning[disabled], fieldset[disabled] .bulma-button.bulma-is-warning {
  background-color: var(--bulma-warning);
  border-color: var(--bulma-warning);
  box-shadow: none;
}
.bulma-button.bulma-is-danger {
  --bulma-button-h: var(--bulma-danger-h);
  --bulma-button-s: var(--bulma-danger-s);
  --bulma-button-l: var(--bulma-danger-l);
  --bulma-button-background-l: var(--bulma-danger-l);
  --bulma-button-border-l: var(--bulma-danger-l);
  --bulma-button-border-width: 0px;
  --bulma-button-color-l: var(--bulma-danger-invert-l);
  --bulma-button-outer-shadow-a: 0;
}
.bulma-button.bulma-is-danger:focus-visible, .bulma-button.bulma-is-danger.bulma-is-focused {
  --bulma-button-border-width: 1px;
}
.bulma-button.bulma-is-danger.bulma-is-light {
  --bulma-button-background-l: var(--bulma-light-l);
  --bulma-button-color-l: var(--bulma-danger-light-invert-l);
}
.bulma-button.bulma-is-danger.bulma-is-dark {
  --bulma-button-background-l: var(--bulma-dark-l);
  --bulma-button-color-l: var(--bulma-danger-dark-invert-l);
}
.bulma-button.bulma-is-danger.bulma-is-soft {
  --bulma-button-background-l: var(--bulma-soft-l);
  --bulma-button-color-l: var(--bulma-soft-invert-l);
}
.bulma-button.bulma-is-danger.bulma-is-bold {
  --bulma-button-background-l: var(--bulma-bold-l);
  --bulma-button-color-l: var(--bulma-bold-invert-l);
}
.bulma-button.bulma-is-danger[disabled], fieldset[disabled] .bulma-button.bulma-is-danger {
  background-color: var(--bulma-danger);
  border-color: var(--bulma-danger);
  box-shadow: none;
}
.bulma-button.bulma-is-outlined {
  --bulma-button-border-width: max(1px, 0.0625em);
  background-color: transparent;
  border-color: hsl(var(--bulma-button-h), var(--bulma-button-s), var(--bulma-button-l));
  color: hsl(var(--bulma-button-h), var(--bulma-button-s), var(--bulma-button-l));
}
.bulma-button.bulma-is-outlined:hover {
  --bulma-button-border-width: max(2px, 0.125em);
  --bulma-button-outer-shadow-alpha: 1;
}
.bulma-button.bulma-is-inverted {
  background-color: hsl(var(--bulma-button-h), var(--bulma-button-s), calc(var(--bulma-button-color-l) + var(--bulma-button-background-l-delta)));
  color: hsl(var(--bulma-button-h), var(--bulma-button-s), var(--bulma-button-background-l));
}
.bulma-button.bulma-is-text {
  background-color: transparent;
  border-color: transparent;
  color: var(--bulma-button-text-color);
  text-decoration: var(--bulma-button-text-decoration);
}
.bulma-button.bulma-is-text:hover, .bulma-button.bulma-is-text.bulma-is-hovered {
  background-color: var(--bulma-button-text-hover-background-color);
  color: var(--bulma-button-text-hover-color);
}
.bulma-button.bulma-is-text:active, .bulma-button.bulma-is-text.bulma-is-active {
  color: var(--bulma-button-text-hover-color);
}
.bulma-button.bulma-is-text[disabled], fieldset[disabled] .bulma-button.bulma-is-text {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}
.bulma-button.bulma-is-ghost {
  background: var(--bulma-button-ghost-background);
  border-color: var(--bulma-button-ghost-border-color);
  box-shadow: none;
  color: var(--bulma-button-ghost-color);
  text-decoration: var(--bulma-button-ghost-decoration);
}
.bulma-button.bulma-is-ghost:hover, .bulma-button.bulma-is-ghost.bulma-is-hovered {
  color: var(--bulma-button-ghost-hover-color);
  text-decoration: var(--bulma-button-ghost-hover-decoration);
}
.bulma-button.bulma-is-small {
  --bulma-control-size: var(--bulma-size-small);
  --bulma-control-radius: var(--bulma-radius-small);
}
.bulma-button.bulma-is-normal {
  --bulma-control-size: var(--bulma-size-normal);
  --bulma-control-radius: var(--bulma-radius);
}
.bulma-button.bulma-is-medium {
  --bulma-control-size: var(--bulma-size-medium);
  --bulma-control-radius: var(--bulma-radius-medium);
}
.bulma-button.bulma-is-large {
  --bulma-control-size: var(--bulma-size-large);
  --bulma-control-radius: var(--bulma-radius-medium);
}
.bulma-button.bulma-is-fullwidth {
  display: flex;
  width: 100%;
}
.bulma-button.bulma-is-loading {
  box-shadow: none;
  color: transparent !important;
  pointer-events: none;
}
.bulma-button.bulma-is-loading::after {
  position: absolute;
  left: calc(50% - 1em * 0.5);
  top: calc(50% - 1em * 0.5);
  position: absolute !important;
}
.bulma-button.bulma-is-static {
  background-color: var(--bulma-button-static-background-color);
  border-color: var(--bulma-button-static-border-color);
  color: var(--bulma-button-static-color);
  box-shadow: none;
  pointer-events: none;
}
.bulma-button.bulma-is-rounded {
  border-radius: var(--bulma-radius-rounded);
  padding-left: calc(var(--bulma-button-padding-horizontal) + 0.25em - var(--bulma-button-border-width));
  padding-right: calc(var(--bulma-button-padding-horizontal) + 0.25em - var(--bulma-button-border-width));
}

.bulma-buttons {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: flex-start;
}
.bulma-buttons.bulma-are-small {
  --bulma-control-size: var(--bulma-size-small);
  --bulma-control-radius: var(--bulma-radius-small);
}
.bulma-buttons.bulma-are-medium {
  --bulma-control-size: var(--bulma-size-medium);
  --bulma-control-radius: var(--bulma-radius-medium);
}
.bulma-buttons.bulma-are-large {
  --bulma-control-size: var(--bulma-size-large);
  --bulma-control-radius: var(--bulma-radius-large);
}
.bulma-buttons.bulma-has-addons {
  gap: 0;
}
.bulma-buttons.bulma-has-addons .bulma-button:not(:first-child) {
  border-end-start-radius: 0;
  border-start-start-radius: 0;
}
.bulma-buttons.bulma-has-addons .bulma-button:not(:last-child) {
  border-end-end-radius: 0;
  border-start-end-radius: 0;
  margin-inline-end: -1px;
}
.bulma-buttons.bulma-has-addons .bulma-button:hover, .bulma-buttons.bulma-has-addons .bulma-button.bulma-is-hovered {
  z-index: 2;
}
.bulma-buttons.bulma-has-addons .bulma-button:focus, .bulma-buttons.bulma-has-addons .bulma-button.bulma-is-focused, .bulma-buttons.bulma-has-addons .bulma-button:active, .bulma-buttons.bulma-has-addons .bulma-button.bulma-is-active, .bulma-buttons.bulma-has-addons .bulma-button.bulma-is-selected {
  z-index: 3;
}
.bulma-buttons.bulma-has-addons .bulma-button:focus:hover, .bulma-buttons.bulma-has-addons .bulma-button.bulma-is-focused:hover, .bulma-buttons.bulma-has-addons .bulma-button:active:hover, .bulma-buttons.bulma-has-addons .bulma-button.bulma-is-active:hover, .bulma-buttons.bulma-has-addons .bulma-button.bulma-is-selected:hover {
  z-index: 4;
}
.bulma-buttons.bulma-has-addons .bulma-button.bulma-is-expanded {
  flex-grow: 1;
  flex-shrink: 1;
}
.bulma-buttons.bulma-is-centered {
  justify-content: center;
}
.bulma-buttons.bulma-is-right {
  justify-content: flex-end;
}

@media screen and (max-width: 768px) {
  .bulma-button.bulma-is-responsive.bulma-is-small {
    font-size: calc(var(--bulma-size-small) * 0.75);
  }
  .bulma-button.bulma-is-responsive,
  .bulma-button.bulma-is-responsive.bulma-is-normal {
    font-size: calc(var(--bulma-size-small) * 0.875);
  }
  .bulma-button.bulma-is-responsive.bulma-is-medium {
    font-size: var(--bulma-size-small);
  }
  .bulma-button.bulma-is-responsive.bulma-is-large {
    font-size: var(--bulma-size-normal);
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-button.bulma-is-responsive.bulma-is-small {
    font-size: calc(var(--bulma-size-small) * 0.875);
  }
  .bulma-button.bulma-is-responsive,
  .bulma-button.bulma-is-responsive.bulma-is-normal {
    font-size: var(--bulma-size-small);
  }
  .bulma-button.bulma-is-responsive.bulma-is-medium {
    font-size: var(--bulma-size-normal);
  }
  .bulma-button.bulma-is-responsive.bulma-is-large {
    font-size: var(--bulma-size-medium);
  }
}
.bulma-content {
  --bulma-content-heading-color: var(--bulma-text-strong);
  --bulma-content-heading-weight: var(--bulma-weight-extrabold);
  --bulma-content-heading-line-height: 1.125;
  --bulma-content-block-margin-bottom: 1em;
  --bulma-content-blockquote-background-color: var(--bulma-background);
  --bulma-content-blockquote-border-left: 5px solid var(--bulma-border);
  --bulma-content-blockquote-padding: 1.25em 1.5em;
  --bulma-content-pre-padding: 1.25em 1.5em;
  --bulma-content-table-cell-border: 1px solid var(--bulma-border);
  --bulma-content-table-cell-border-width: 0 0 1px;
  --bulma-content-table-cell-padding: 0.5em 0.75em;
  --bulma-content-table-cell-heading-color: var(--bulma-text-strong);
  --bulma-content-table-head-cell-border-width: 0 0 2px;
  --bulma-content-table-head-cell-color: var(--bulma-text-strong);
  --bulma-content-table-body-last-row-cell-border-bottom-width: 0;
  --bulma-content-table-foot-cell-border-width: 2px 0 0;
  --bulma-content-table-foot-cell-color: var(--bulma-text-strong);
}

.bulma-content li + li {
  margin-top: 0.25em;
}
.bulma-content p:not(:last-child),
.bulma-content dl:not(:last-child),
.bulma-content ol:not(:last-child),
.bulma-content ul:not(:last-child),
.bulma-content blockquote:not(:last-child),
.bulma-content pre:not(:last-child),
.bulma-content table:not(:last-child) {
  margin-bottom: var(--bulma-content-block-margin-bottom);
}
.bulma-content h1,
.bulma-content h2,
.bulma-content h3,
.bulma-content h4,
.bulma-content h5,
.bulma-content h6 {
  color: var(--bulma-content-heading-color);
  font-weight: var(--bulma-content-heading-weight);
  line-height: var(--bulma-content-heading-line-height);
}
.bulma-content h1 {
  font-size: 2em;
  margin-bottom: 0.5em;
}
.bulma-content h1:not(:first-child) {
  margin-top: 1em;
}
.bulma-content h2 {
  font-size: 1.75em;
  margin-bottom: 0.5714em;
}
.bulma-content h2:not(:first-child) {
  margin-top: 1.1428em;
}
.bulma-content h3 {
  font-size: 1.5em;
  margin-bottom: 0.6666em;
}
.bulma-content h3:not(:first-child) {
  margin-top: 1.3333em;
}
.bulma-content h4 {
  font-size: 1.25em;
  margin-bottom: 0.8em;
}
.bulma-content h5 {
  font-size: 1.125em;
  margin-bottom: 0.8888em;
}
.bulma-content h6 {
  font-size: 1em;
  margin-bottom: 1em;
}
.bulma-content blockquote {
  background-color: var(--bulma-content-blockquote-background-color);
  border-inline-start: var(--bulma-content-blockquote-border-left);
  padding: var(--bulma-content-blockquote-padding);
}
.bulma-content ol {
  list-style-position: outside;
  margin-inline-start: 2em;
}
.bulma-content ol:not(:first-child) {
  margin-top: 1em;
}
.bulma-content ol:not([type]) {
  list-style-type: decimal;
}
.bulma-content ol:not([type]).bulma-is-lower-alpha {
  list-style-type: lower-alpha;
}
.bulma-content ol:not([type]).bulma-is-lower-roman {
  list-style-type: lower-roman;
}
.bulma-content ol:not([type]).bulma-is-upper-alpha {
  list-style-type: upper-alpha;
}
.bulma-content ol:not([type]).bulma-is-upper-roman {
  list-style-type: upper-roman;
}
.bulma-content ul {
  list-style: disc outside;
  margin-inline-start: 2em;
}
.bulma-content ul:not(:first-child) {
  margin-top: 1em;
}
.bulma-content ul ul {
  list-style-type: circle;
  margin-bottom: 0.25em;
  margin-top: 0.25em;
}
.bulma-content ul ul ul {
  list-style-type: square;
}
.bulma-content dd {
  margin-inline-start: 2em;
}
.bulma-content figure:not([class]) {
  margin-left: 2em;
  margin-right: 2em;
  text-align: center;
}
.bulma-content figure:not([class]):not(:first-child) {
  margin-top: 2em;
}
.bulma-content figure:not([class]):not(:last-child) {
  margin-bottom: 2em;
}
.bulma-content figure:not([class]) img {
  display: inline-block;
}
.bulma-content figure:not([class]) figcaption {
  font-style: italic;
}
.bulma-content pre {
  -webkit-overflow-scrolling: touch;
  overflow-x: auto;
  padding: var(--bulma-content-pre-padding);
  white-space: pre;
  word-wrap: normal;
}
.bulma-content sup,
.bulma-content sub {
  font-size: 75%;
}
.bulma-content table td,
.bulma-content table th {
  border: var(--bulma-content-table-cell-border);
  border-width: var(--bulma-content-table-cell-border-width);
  padding: var(--bulma-content-table-cell-padding);
  vertical-align: top;
}
.bulma-content table th {
  color: var(--bulma-content-table-cell-heading-color);
}
.bulma-content table th:not([align]) {
  text-align: inherit;
}
.bulma-content table thead td,
.bulma-content table thead th {
  border-width: var(--bulma-content-table-head-cell-border-width);
  color: var(--bulma-content-table-head-cell-color);
}
.bulma-content table tfoot td,
.bulma-content table tfoot th {
  border-width: var(--bulma-content-table-foot-cell-border-width);
  color: var(--bulma-content-table-foot-cell-color);
}
.bulma-content table tbody tr:last-child td,
.bulma-content table tbody tr:last-child th {
  border-bottom-width: var(--bulma-content-table-body-last-row-cell-border-bottom-width);
}
.bulma-content .bulma-tabs li + li {
  margin-top: 0;
}
.bulma-content.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-content.bulma-is-normal {
  font-size: var(--bulma-size-normal);
}
.bulma-content.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-content.bulma-is-large {
  font-size: var(--bulma-size-large);
}

.bulma-delete {
  --bulma-delete-dimensions: 1.25rem;
  --bulma-delete-background-l: 0%;
  --bulma-delete-background-alpha: 0.5;
  --bulma-delete-color: var(--bulma-white);
  appearance: none;
  background-color: hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-delete-background-l), var(--bulma-delete-background-alpha));
  border: none;
  border-radius: var(--bulma-radius-rounded);
  cursor: pointer;
  pointer-events: auto;
  display: inline-flex;
  flex-grow: 0;
  flex-shrink: 0;
  font-size: 1em;
  height: var(--bulma-delete-dimensions);
  max-height: var(--bulma-delete-dimensions);
  max-width: var(--bulma-delete-dimensions);
  min-height: var(--bulma-delete-dimensions);
  min-width: var(--bulma-delete-dimensions);
  outline: none;
  position: relative;
  vertical-align: top;
  width: var(--bulma-delete-dimensions);
}
.bulma-delete::before, .bulma-delete::after {
  background-color: var(--bulma-delete-color);
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform-origin: center center;
}
.bulma-delete::before {
  height: 2px;
  width: 50%;
}
.bulma-delete::after {
  height: 50%;
  width: 2px;
}
.bulma-delete:hover, .bulma-delete:focus {
  --bulma-delete-background-alpha: 0.4;
}
.bulma-delete:active {
  --bulma-delete-background-alpha: 0.5;
}
.bulma-delete.bulma-is-small {
  --bulma-delete-dimensions: 1rem;
}
.bulma-delete.bulma-is-medium {
  --bulma-delete-dimensions: 1.5rem;
}
.bulma-delete.bulma-is-large {
  --bulma-delete-dimensions: 2rem;
}

.bulma-icon,
.bulma-icon-text {
  --bulma-icon-dimensions: 1.5rem;
  --bulma-icon-dimensions-small: 1rem;
  --bulma-icon-dimensions-medium: 2rem;
  --bulma-icon-dimensions-large: 3rem;
  --bulma-icon-text-spacing: 0.25em;
}

.bulma-icon {
  align-items: center;
  display: inline-flex;
  flex-shrink: 0;
  justify-content: center;
  height: var(--bulma-icon-dimensions);
  transition-duration: var(--bulma-duration);
  transition-property: color;
  width: var(--bulma-icon-dimensions);
}
.bulma-icon.bulma-is-small {
  height: var(--bulma-icon-dimensions-small);
  width: var(--bulma-icon-dimensions-small);
}
.bulma-icon.bulma-is-medium {
  height: var(--bulma-icon-dimensions-medium);
  width: var(--bulma-icon-dimensions-medium);
}
.bulma-icon.bulma-is-large {
  height: var(--bulma-icon-dimensions-large);
  width: var(--bulma-icon-dimensions-large);
}

.bulma-icon-text {
  align-items: flex-start;
  color: inherit;
  display: inline-flex;
  flex-wrap: wrap;
  gap: var(--bulma-icon-text-spacing);
  line-height: var(--bulma-icon-dimensions);
  vertical-align: top;
}
.bulma-icon-text .bulma-icon {
  flex-grow: 0;
  flex-shrink: 0;
}

div.bulma-icon-text {
  display: flex;
}

.bulma-image {
  display: block;
  position: relative;
}
.bulma-image img {
  display: block;
  height: auto;
  width: 100%;
}
.bulma-image img.bulma-is-rounded {
  border-radius: var(--bulma-radius-rounded);
}
.bulma-image.bulma-is-fullwidth {
  width: 100%;
}
.bulma-image.bulma-is-square img,
.bulma-image.bulma-is-square .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-square {
  aspect-ratio: 1;
}
.bulma-image.bulma-is-1by1 {
  aspect-ratio: 1/1;
}
.bulma-image.bulma-is-1by1 img,
.bulma-image.bulma-is-1by1 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-5by4 {
  aspect-ratio: 5/4;
}
.bulma-image.bulma-is-5by4 img,
.bulma-image.bulma-is-5by4 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-4by3 {
  aspect-ratio: 4/3;
}
.bulma-image.bulma-is-4by3 img,
.bulma-image.bulma-is-4by3 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-3by2 {
  aspect-ratio: 3/2;
}
.bulma-image.bulma-is-3by2 img,
.bulma-image.bulma-is-3by2 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-5by3 {
  aspect-ratio: 5/3;
}
.bulma-image.bulma-is-5by3 img,
.bulma-image.bulma-is-5by3 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-16by9 {
  aspect-ratio: 16/9;
}
.bulma-image.bulma-is-16by9 img,
.bulma-image.bulma-is-16by9 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-2by1 {
  aspect-ratio: 2/1;
}
.bulma-image.bulma-is-2by1 img,
.bulma-image.bulma-is-2by1 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-3by1 {
  aspect-ratio: 3/1;
}
.bulma-image.bulma-is-3by1 img,
.bulma-image.bulma-is-3by1 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-4by5 {
  aspect-ratio: 4/5;
}
.bulma-image.bulma-is-4by5 img,
.bulma-image.bulma-is-4by5 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-3by4 {
  aspect-ratio: 3/4;
}
.bulma-image.bulma-is-3by4 img,
.bulma-image.bulma-is-3by4 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-2by3 {
  aspect-ratio: 2/3;
}
.bulma-image.bulma-is-2by3 img,
.bulma-image.bulma-is-2by3 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-3by5 {
  aspect-ratio: 3/5;
}
.bulma-image.bulma-is-3by5 img,
.bulma-image.bulma-is-3by5 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-9by16 {
  aspect-ratio: 9/16;
}
.bulma-image.bulma-is-9by16 img,
.bulma-image.bulma-is-9by16 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-1by2 {
  aspect-ratio: 1/2;
}
.bulma-image.bulma-is-1by2 img,
.bulma-image.bulma-is-1by2 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-1by3 {
  aspect-ratio: 1/3;
}
.bulma-image.bulma-is-1by3 img,
.bulma-image.bulma-is-1by3 .bulma-has-ratio {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.bulma-image.bulma-is-16x16 {
  height: 16px;
  width: 16px;
}
.bulma-image.bulma-is-24x24 {
  height: 24px;
  width: 24px;
}
.bulma-image.bulma-is-32x32 {
  height: 32px;
  width: 32px;
}
.bulma-image.bulma-is-48x48 {
  height: 48px;
  width: 48px;
}
.bulma-image.bulma-is-64x64 {
  height: 64px;
  width: 64px;
}
.bulma-image.bulma-is-96x96 {
  height: 96px;
  width: 96px;
}
.bulma-image.bulma-is-128x128 {
  height: 128px;
  width: 128px;
}

.bulma-loader {
  animation: spinAround 500ms infinite linear;
  border: 2px solid var(--bulma-border);
  border-radius: var(--bulma-radius-rounded);
  border-right-color: transparent;
  border-top-color: transparent;
  content: "";
  display: block;
  height: 1em;
  position: relative;
  width: 1em;
}

.bulma-notification {
  --bulma-notification-h: var(--bulma-scheme-h);
  --bulma-notification-s: var(--bulma-scheme-s);
  --bulma-notification-background-l: var(--bulma-background-l);
  --bulma-notification-color-l: var(--bulma-text-strong-l);
  --bulma-notification-code-background-color: var(--bulma-scheme-main);
  --bulma-notification-radius: var(--bulma-radius);
  --bulma-notification-padding: 1.375em 1.5em;
}

.bulma-notification {
  background-color: hsl(var(--bulma-notification-h), var(--bulma-notification-s), var(--bulma-notification-background-l));
  border-radius: var(--bulma-notification-radius);
  color: hsl(var(--bulma-notification-h), var(--bulma-notification-s), var(--bulma-notification-color-l));
  padding: var(--bulma-notification-padding);
  position: relative;
}
.bulma-notification a:not(.bulma-button):not(.bulma-dropdown-item) {
  color: currentColor;
  text-decoration: underline;
}
.bulma-notification strong {
  color: currentColor;
}
.bulma-notification code,
.bulma-notification pre {
  background: var(--bulma-notification-code-background-color);
}
.bulma-notification pre code {
  background: transparent;
}
.bulma-notification > .bulma-delete {
  position: absolute;
  inset-inline-end: 1rem;
  top: 1rem;
}
.bulma-notification .bulma-title,
.bulma-notification .bulma-subtitle,
.bulma-notification .bulma-content {
  color: currentColor;
}
.bulma-notification.bulma-is-white {
  --bulma-notification-h: var(--bulma-white-h);
  --bulma-notification-s: var(--bulma-white-s);
  --bulma-notification-background-l: var(--bulma-white-l);
  --bulma-notification-color-l: var(--bulma-white-invert-l);
}
.bulma-notification.bulma-is-white.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-white-light-invert-l);
}
.bulma-notification.bulma-is-white.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-white-dark-invert-l);
}
.bulma-notification.bulma-is-black {
  --bulma-notification-h: var(--bulma-black-h);
  --bulma-notification-s: var(--bulma-black-s);
  --bulma-notification-background-l: var(--bulma-black-l);
  --bulma-notification-color-l: var(--bulma-black-invert-l);
}
.bulma-notification.bulma-is-black.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-black-light-invert-l);
}
.bulma-notification.bulma-is-black.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-black-dark-invert-l);
}
.bulma-notification.bulma-is-light {
  --bulma-notification-h: var(--bulma-light-h);
  --bulma-notification-s: var(--bulma-light-s);
  --bulma-notification-background-l: var(--bulma-light-l);
  --bulma-notification-color-l: var(--bulma-light-invert-l);
}
.bulma-notification.bulma-is-light.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-light-light-invert-l);
}
.bulma-notification.bulma-is-light.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-light-dark-invert-l);
}
.bulma-notification.bulma-is-dark {
  --bulma-notification-h: var(--bulma-dark-h);
  --bulma-notification-s: var(--bulma-dark-s);
  --bulma-notification-background-l: var(--bulma-dark-l);
  --bulma-notification-color-l: var(--bulma-dark-invert-l);
}
.bulma-notification.bulma-is-dark.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-dark-light-invert-l);
}
.bulma-notification.bulma-is-dark.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-dark-dark-invert-l);
}
.bulma-notification.bulma-is-text {
  --bulma-notification-h: var(--bulma-text-h);
  --bulma-notification-s: var(--bulma-text-s);
  --bulma-notification-background-l: var(--bulma-text-l);
  --bulma-notification-color-l: var(--bulma-text-invert-l);
}
.bulma-notification.bulma-is-text.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-text-light-invert-l);
}
.bulma-notification.bulma-is-text.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-text-dark-invert-l);
}
.bulma-notification.bulma-is-primary {
  --bulma-notification-h: var(--bulma-primary-h);
  --bulma-notification-s: var(--bulma-primary-s);
  --bulma-notification-background-l: var(--bulma-primary-l);
  --bulma-notification-color-l: var(--bulma-primary-invert-l);
}
.bulma-notification.bulma-is-primary.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-primary-light-invert-l);
}
.bulma-notification.bulma-is-primary.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-primary-dark-invert-l);
}
.bulma-notification.bulma-is-link {
  --bulma-notification-h: var(--bulma-link-h);
  --bulma-notification-s: var(--bulma-link-s);
  --bulma-notification-background-l: var(--bulma-link-l);
  --bulma-notification-color-l: var(--bulma-link-invert-l);
}
.bulma-notification.bulma-is-link.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-link-light-invert-l);
}
.bulma-notification.bulma-is-link.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-link-dark-invert-l);
}
.bulma-notification.bulma-is-info {
  --bulma-notification-h: var(--bulma-info-h);
  --bulma-notification-s: var(--bulma-info-s);
  --bulma-notification-background-l: var(--bulma-info-l);
  --bulma-notification-color-l: var(--bulma-info-invert-l);
}
.bulma-notification.bulma-is-info.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-info-light-invert-l);
}
.bulma-notification.bulma-is-info.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-info-dark-invert-l);
}
.bulma-notification.bulma-is-success {
  --bulma-notification-h: var(--bulma-success-h);
  --bulma-notification-s: var(--bulma-success-s);
  --bulma-notification-background-l: var(--bulma-success-l);
  --bulma-notification-color-l: var(--bulma-success-invert-l);
}
.bulma-notification.bulma-is-success.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-success-light-invert-l);
}
.bulma-notification.bulma-is-success.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-success-dark-invert-l);
}
.bulma-notification.bulma-is-warning {
  --bulma-notification-h: var(--bulma-warning-h);
  --bulma-notification-s: var(--bulma-warning-s);
  --bulma-notification-background-l: var(--bulma-warning-l);
  --bulma-notification-color-l: var(--bulma-warning-invert-l);
}
.bulma-notification.bulma-is-warning.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-warning-light-invert-l);
}
.bulma-notification.bulma-is-warning.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-warning-dark-invert-l);
}
.bulma-notification.bulma-is-danger {
  --bulma-notification-h: var(--bulma-danger-h);
  --bulma-notification-s: var(--bulma-danger-s);
  --bulma-notification-background-l: var(--bulma-danger-l);
  --bulma-notification-color-l: var(--bulma-danger-invert-l);
}
.bulma-notification.bulma-is-danger.bulma-is-light {
  --bulma-notification-background-l: 90%;
  --bulma-notification-color-l: var(--bulma-danger-light-invert-l);
}
.bulma-notification.bulma-is-danger.bulma-is-dark {
  --bulma-notification-background-l: 20%;
  --bulma-notification-color-l: var(--bulma-danger-dark-invert-l);
}

.bulma-progress {
  --bulma-progress-border-radius: var(--bulma-radius-rounded);
  --bulma-progress-bar-background-color: var(--bulma-border-weak);
  --bulma-progress-value-background-color: var(--bulma-text);
  --bulma-progress-indeterminate-duration: 1.5s;
}

.bulma-progress {
  appearance: none;
  border: none;
  border-radius: var(--bulma-progress-border-radius);
  display: block;
  height: var(--bulma-size-normal);
  overflow: hidden;
  padding: 0;
  width: 100%;
}
.bulma-progress::-webkit-progress-bar {
  background-color: var(--bulma-progress-bar-background-color);
}
.bulma-progress::-webkit-progress-value {
  background-color: var(--bulma-progress-value-background-color);
}
.bulma-progress::-moz-progress-bar {
  background-color: var(--bulma-progress-value-background-color);
}
.bulma-progress::-ms-fill {
  background-color: var(--bulma-progress-value-background-color);
  border: none;
}
.bulma-progress.bulma-is-white {
  --bulma-progress-value-background-color: var(--bulma-white);
}
.bulma-progress.bulma-is-black {
  --bulma-progress-value-background-color: var(--bulma-black);
}
.bulma-progress.bulma-is-light {
  --bulma-progress-value-background-color: var(--bulma-light);
}
.bulma-progress.bulma-is-dark {
  --bulma-progress-value-background-color: var(--bulma-dark);
}
.bulma-progress.bulma-is-text {
  --bulma-progress-value-background-color: var(--bulma-text);
}
.bulma-progress.bulma-is-primary {
  --bulma-progress-value-background-color: var(--bulma-primary);
}
.bulma-progress.bulma-is-link {
  --bulma-progress-value-background-color: var(--bulma-link);
}
.bulma-progress.bulma-is-info {
  --bulma-progress-value-background-color: var(--bulma-info);
}
.bulma-progress.bulma-is-success {
  --bulma-progress-value-background-color: var(--bulma-success);
}
.bulma-progress.bulma-is-warning {
  --bulma-progress-value-background-color: var(--bulma-warning);
}
.bulma-progress.bulma-is-danger {
  --bulma-progress-value-background-color: var(--bulma-danger);
}
.bulma-progress:indeterminate {
  animation-duration: var(--bulma-progress-indeterminate-duration);
  animation-iteration-count: infinite;
  animation-name: moveIndeterminate;
  animation-timing-function: linear;
  background-color: var(--bulma-progress-bar-background-color);
  background-image: linear-gradient(to right, var(--bulma-progress-value-background-color) 30%, var(--bulma-progress-bar-background-color) 30%);
  background-position: top left;
  background-repeat: no-repeat;
  background-size: 150% 150%;
}
.bulma-progress:indeterminate::-webkit-progress-bar {
  background-color: transparent;
}
.bulma-progress:indeterminate::-moz-progress-bar {
  background-color: transparent;
}
.bulma-progress:indeterminate::-ms-fill {
  animation-name: none;
}
.bulma-progress.bulma-is-small {
  height: var(--bulma-size-small);
}
.bulma-progress.bulma-is-medium {
  height: var(--bulma-size-medium);
}
.bulma-progress.bulma-is-large {
  height: var(--bulma-size-large);
}

@keyframes moveIndeterminate {
  from {
    background-position: 200% 0;
  }
  to {
    background-position: -200% 0;
  }
}
.bulma-table {
  --bulma-table-color: var(--bulma-text-strong);
  --bulma-table-background-color: var(--bulma-scheme-main);
  --bulma-table-cell-border-color: var(--bulma-border);
  --bulma-table-cell-border-style: solid;
  --bulma-table-cell-border-width: 0 0 1px;
  --bulma-table-cell-padding: 0.5em 0.75em;
  --bulma-table-cell-heading-color: var(--bulma-text-strong);
  --bulma-table-cell-text-align: left;
  --bulma-table-head-cell-border-width: 0 0 2px;
  --bulma-table-head-cell-color: var(--bulma-text-strong);
  --bulma-table-foot-cell-border-width: 2px 0 0;
  --bulma-table-foot-cell-color: var(--bulma-text-strong);
  --bulma-table-head-background-color: transparent;
  --bulma-table-body-background-color: transparent;
  --bulma-table-foot-background-color: transparent;
  --bulma-table-row-hover-background-color: var(--bulma-scheme-main-bis);
  --bulma-table-row-active-background-color: var(--bulma-primary);
  --bulma-table-row-active-color: var(--bulma-primary-invert);
  --bulma-table-striped-row-even-background-color: var(--bulma-scheme-main-bis);
  --bulma-table-striped-row-even-hover-background-color: var(--bulma-scheme-main-ter);
}

.bulma-table {
  background-color: var(--bulma-table-background-color);
  color: var(--bulma-table-color);
}
.bulma-table td,
.bulma-table th {
  background-color: var(--bulma-table-cell-background-color);
  border-color: var(--bulma-table-cell-border-color);
  border-style: var(--bulma-table-cell-border-style);
  border-width: var(--bulma-table-cell-border-width);
  color: var(--bulma-table-color);
  padding: var(--bulma-table-cell-padding);
  vertical-align: top;
}
.bulma-table td.bulma-is-white,
.bulma-table th.bulma-is-white {
  --bulma-table-color: var(--bulma-white-invert);
  --bulma-table-cell-heading-color: var(--bulma-white-invert);
  --bulma-table-cell-background-color: var(--bulma-white);
  --bulma-table-cell-border-color: var(--bulma-white);
}
.bulma-table td.bulma-is-black,
.bulma-table th.bulma-is-black {
  --bulma-table-color: var(--bulma-black-invert);
  --bulma-table-cell-heading-color: var(--bulma-black-invert);
  --bulma-table-cell-background-color: var(--bulma-black);
  --bulma-table-cell-border-color: var(--bulma-black);
}
.bulma-table td.bulma-is-light,
.bulma-table th.bulma-is-light {
  --bulma-table-color: var(--bulma-light-invert);
  --bulma-table-cell-heading-color: var(--bulma-light-invert);
  --bulma-table-cell-background-color: var(--bulma-light);
  --bulma-table-cell-border-color: var(--bulma-light);
}
.bulma-table td.bulma-is-dark,
.bulma-table th.bulma-is-dark {
  --bulma-table-color: var(--bulma-dark-invert);
  --bulma-table-cell-heading-color: var(--bulma-dark-invert);
  --bulma-table-cell-background-color: var(--bulma-dark);
  --bulma-table-cell-border-color: var(--bulma-dark);
}
.bulma-table td.bulma-is-text,
.bulma-table th.bulma-is-text {
  --bulma-table-color: var(--bulma-text-invert);
  --bulma-table-cell-heading-color: var(--bulma-text-invert);
  --bulma-table-cell-background-color: var(--bulma-text);
  --bulma-table-cell-border-color: var(--bulma-text);
}
.bulma-table td.bulma-is-primary,
.bulma-table th.bulma-is-primary {
  --bulma-table-color: var(--bulma-primary-invert);
  --bulma-table-cell-heading-color: var(--bulma-primary-invert);
  --bulma-table-cell-background-color: var(--bulma-primary);
  --bulma-table-cell-border-color: var(--bulma-primary);
}
.bulma-table td.bulma-is-link,
.bulma-table th.bulma-is-link {
  --bulma-table-color: var(--bulma-link-invert);
  --bulma-table-cell-heading-color: var(--bulma-link-invert);
  --bulma-table-cell-background-color: var(--bulma-link);
  --bulma-table-cell-border-color: var(--bulma-link);
}
.bulma-table td.bulma-is-info,
.bulma-table th.bulma-is-info {
  --bulma-table-color: var(--bulma-info-invert);
  --bulma-table-cell-heading-color: var(--bulma-info-invert);
  --bulma-table-cell-background-color: var(--bulma-info);
  --bulma-table-cell-border-color: var(--bulma-info);
}
.bulma-table td.bulma-is-success,
.bulma-table th.bulma-is-success {
  --bulma-table-color: var(--bulma-success-invert);
  --bulma-table-cell-heading-color: var(--bulma-success-invert);
  --bulma-table-cell-background-color: var(--bulma-success);
  --bulma-table-cell-border-color: var(--bulma-success);
}
.bulma-table td.bulma-is-warning,
.bulma-table th.bulma-is-warning {
  --bulma-table-color: var(--bulma-warning-invert);
  --bulma-table-cell-heading-color: var(--bulma-warning-invert);
  --bulma-table-cell-background-color: var(--bulma-warning);
  --bulma-table-cell-border-color: var(--bulma-warning);
}
.bulma-table td.bulma-is-danger,
.bulma-table th.bulma-is-danger {
  --bulma-table-color: var(--bulma-danger-invert);
  --bulma-table-cell-heading-color: var(--bulma-danger-invert);
  --bulma-table-cell-background-color: var(--bulma-danger);
  --bulma-table-cell-border-color: var(--bulma-danger);
}
.bulma-table td.bulma-is-narrow,
.bulma-table th.bulma-is-narrow {
  white-space: nowrap;
  width: 1%;
}
.bulma-table td.bulma-is-selected,
.bulma-table th.bulma-is-selected {
  background-color: var(--bulma-table-row-active-background-color);
  color: var(--bulma-table-row-active-color);
}
.bulma-table td.bulma-is-selected a,
.bulma-table td.bulma-is-selected strong,
.bulma-table th.bulma-is-selected a,
.bulma-table th.bulma-is-selected strong {
  color: currentColor;
}
.bulma-table td.bulma-is-vcentered,
.bulma-table th.bulma-is-vcentered {
  vertical-align: middle;
}
.bulma-table th {
  color: var(--bulma-table-cell-heading-color);
}
.bulma-table th:not([align]) {
  text-align: var(--bulma-table-cell-text-align);
}
.bulma-table tr.bulma-is-selected {
  background-color: var(--bulma-table-row-active-background-color);
  color: var(--bulma-table-row-active-color);
}
.bulma-table tr.bulma-is-selected a,
.bulma-table tr.bulma-is-selected strong {
  color: currentColor;
}
.bulma-table tr.bulma-is-selected td,
.bulma-table tr.bulma-is-selected th {
  border-color: var(--bulma-table-row-active-color);
  color: currentColor;
}
.bulma-table tr.bulma-is-white {
  --bulma-table-color: var(--bulma-white-invert);
  --bulma-table-cell-heading-color: var(--bulma-white-invert);
  --bulma-table-cell-background-color: var(--bulma-white);
  --bulma-table-cell-border-color: var(--bulma-white);
}
.bulma-table tr.bulma-is-black {
  --bulma-table-color: var(--bulma-black-invert);
  --bulma-table-cell-heading-color: var(--bulma-black-invert);
  --bulma-table-cell-background-color: var(--bulma-black);
  --bulma-table-cell-border-color: var(--bulma-black);
}
.bulma-table tr.bulma-is-light {
  --bulma-table-color: var(--bulma-light-invert);
  --bulma-table-cell-heading-color: var(--bulma-light-invert);
  --bulma-table-cell-background-color: var(--bulma-light);
  --bulma-table-cell-border-color: var(--bulma-light);
}
.bulma-table tr.bulma-is-dark {
  --bulma-table-color: var(--bulma-dark-invert);
  --bulma-table-cell-heading-color: var(--bulma-dark-invert);
  --bulma-table-cell-background-color: var(--bulma-dark);
  --bulma-table-cell-border-color: var(--bulma-dark);
}
.bulma-table tr.bulma-is-text {
  --bulma-table-color: var(--bulma-text-invert);
  --bulma-table-cell-heading-color: var(--bulma-text-invert);
  --bulma-table-cell-background-color: var(--bulma-text);
  --bulma-table-cell-border-color: var(--bulma-text);
}
.bulma-table tr.bulma-is-primary {
  --bulma-table-color: var(--bulma-primary-invert);
  --bulma-table-cell-heading-color: var(--bulma-primary-invert);
  --bulma-table-cell-background-color: var(--bulma-primary);
  --bulma-table-cell-border-color: var(--bulma-primary);
}
.bulma-table tr.bulma-is-link {
  --bulma-table-color: var(--bulma-link-invert);
  --bulma-table-cell-heading-color: var(--bulma-link-invert);
  --bulma-table-cell-background-color: var(--bulma-link);
  --bulma-table-cell-border-color: var(--bulma-link);
}
.bulma-table tr.bulma-is-info {
  --bulma-table-color: var(--bulma-info-invert);
  --bulma-table-cell-heading-color: var(--bulma-info-invert);
  --bulma-table-cell-background-color: var(--bulma-info);
  --bulma-table-cell-border-color: var(--bulma-info);
}
.bulma-table tr.bulma-is-success {
  --bulma-table-color: var(--bulma-success-invert);
  --bulma-table-cell-heading-color: var(--bulma-success-invert);
  --bulma-table-cell-background-color: var(--bulma-success);
  --bulma-table-cell-border-color: var(--bulma-success);
}
.bulma-table tr.bulma-is-warning {
  --bulma-table-color: var(--bulma-warning-invert);
  --bulma-table-cell-heading-color: var(--bulma-warning-invert);
  --bulma-table-cell-background-color: var(--bulma-warning);
  --bulma-table-cell-border-color: var(--bulma-warning);
}
.bulma-table tr.bulma-is-danger {
  --bulma-table-color: var(--bulma-danger-invert);
  --bulma-table-cell-heading-color: var(--bulma-danger-invert);
  --bulma-table-cell-background-color: var(--bulma-danger);
  --bulma-table-cell-border-color: var(--bulma-danger);
}
.bulma-table thead {
  background-color: var(--bulma-table-head-background-color);
}
.bulma-table thead td,
.bulma-table thead th {
  border-width: var(--bulma-table-head-cell-border-width);
  color: var(--bulma-table-head-cell-color);
}
.bulma-table tfoot {
  background-color: var(--bulma-table-foot-background-color);
}
.bulma-table tfoot td,
.bulma-table tfoot th {
  border-width: var(--bulma-table-foot-cell-border-width);
  color: var(--bulma-table-foot-cell-color);
}
.bulma-table tbody {
  background-color: var(--bulma-table-body-background-color);
}
.bulma-table tbody tr:last-child td,
.bulma-table tbody tr:last-child th {
  border-bottom-width: 0;
}
.bulma-table.bulma-is-bordered td,
.bulma-table.bulma-is-bordered th {
  border-width: 1px;
}
.bulma-table.bulma-is-bordered tr:last-child td,
.bulma-table.bulma-is-bordered tr:last-child th {
  border-bottom-width: 1px;
}
.bulma-table.bulma-is-fullwidth {
  width: 100%;
}
.bulma-table.bulma-is-hoverable tbody tr:not(.bulma-is-selected):hover {
  background-color: var(--bulma-table-row-hover-background-color);
}
.bulma-table.bulma-is-hoverable.bulma-is-striped tbody tr:not(.bulma-is-selected):hover {
  background-color: var(--bulma-table-row-hover-background-color);
}
.bulma-table.bulma-is-hoverable.bulma-is-striped tbody tr:not(.bulma-is-selected):hover:nth-child(even) {
  background-color: var(--bulma-table-striped-row-even-hover-background-color);
}
.bulma-table.bulma-is-narrow td,
.bulma-table.bulma-is-narrow th {
  padding: 0.25em 0.5em;
}
.bulma-table.bulma-is-striped tbody tr:not(.bulma-is-selected):nth-child(even) {
  background-color: var(--bulma-table-striped-row-even-background-color);
}

.bulma-table-container {
  -webkit-overflow-scrolling: touch;
  overflow: auto;
  overflow-y: hidden;
  max-width: 100%;
}

.bulma-tags {
  align-items: center;
  color: hsl(var(--bulma-tag-h), var(--bulma-tag-s), var(--bulma-tag-color-l));
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: flex-start;
}
.bulma-tags.bulma-are-medium .bulma-tag:not(.bulma-is-normal):not(.bulma-is-large) {
  font-size: var(--bulma-size-normal);
}
.bulma-tags.bulma-are-large .bulma-tag:not(.bulma-is-normal):not(.bulma-is-medium) {
  font-size: var(--bulma-size-medium);
}
.bulma-tags.bulma-is-centered {
  gap: 0.25rem;
  justify-content: center;
}
.bulma-tags.bulma-is-right {
  justify-content: flex-end;
}
.bulma-tags.bulma-has-addons {
  gap: 0;
}
.bulma-tags.bulma-has-addons .bulma-tag:not(:first-child) {
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}
.bulma-tags.bulma-has-addons .bulma-tag:not(:last-child) {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}

.bulma-tag {
  --bulma-tag-h: var(--bulma-scheme-h);
  --bulma-tag-s: var(--bulma-scheme-s);
  --bulma-tag-background-l: var(--bulma-background-l);
  --bulma-tag-background-l-delta: 0%;
  --bulma-tag-hover-background-l-delta: var(--bulma-hover-background-l-delta);
  --bulma-tag-active-background-l-delta: var(--bulma-active-background-l-delta);
  --bulma-tag-color-l: var(--bulma-text-l);
  --bulma-tag-radius: var(--bulma-radius);
  --bulma-tag-delete-margin: 1px;
  align-items: center;
  background-color: hsl(var(--bulma-tag-h), var(--bulma-tag-s), calc(var(--bulma-tag-background-l) + var(--bulma-tag-background-l-delta)));
  border-radius: var(--bulma-radius);
  color: hsl(var(--bulma-tag-h), var(--bulma-tag-s), var(--bulma-tag-color-l));
  display: inline-flex;
  font-size: var(--bulma-size-small);
  height: 2em;
  justify-content: center;
  line-height: 1.5;
  padding-left: 0.75em;
  padding-right: 0.75em;
  white-space: nowrap;
}
.bulma-tag .bulma-delete {
  margin-inline-start: 0.25rem;
  margin-inline-end: -0.375rem;
}
.bulma-tag.bulma-is-white {
  --bulma-tag-h: var(--bulma-white-h);
  --bulma-tag-s: var(--bulma-white-s);
  --bulma-tag-background-l: var(--bulma-white-l);
  --bulma-tag-color-l: var(--bulma-white-invert-l);
}
.bulma-tag.bulma-is-white.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-white-light-invert-l);
}
.bulma-tag.bulma-is-black {
  --bulma-tag-h: var(--bulma-black-h);
  --bulma-tag-s: var(--bulma-black-s);
  --bulma-tag-background-l: var(--bulma-black-l);
  --bulma-tag-color-l: var(--bulma-black-invert-l);
}
.bulma-tag.bulma-is-black.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-black-light-invert-l);
}
.bulma-tag.bulma-is-light {
  --bulma-tag-h: var(--bulma-light-h);
  --bulma-tag-s: var(--bulma-light-s);
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-light-invert-l);
}
.bulma-tag.bulma-is-light.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-light-light-invert-l);
}
.bulma-tag.bulma-is-dark {
  --bulma-tag-h: var(--bulma-dark-h);
  --bulma-tag-s: var(--bulma-dark-s);
  --bulma-tag-background-l: var(--bulma-dark-l);
  --bulma-tag-color-l: var(--bulma-dark-invert-l);
}
.bulma-tag.bulma-is-dark.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-dark-light-invert-l);
}
.bulma-tag.bulma-is-text {
  --bulma-tag-h: var(--bulma-text-h);
  --bulma-tag-s: var(--bulma-text-s);
  --bulma-tag-background-l: var(--bulma-text-l);
  --bulma-tag-color-l: var(--bulma-text-invert-l);
}
.bulma-tag.bulma-is-text.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-text-light-invert-l);
}
.bulma-tag.bulma-is-primary {
  --bulma-tag-h: var(--bulma-primary-h);
  --bulma-tag-s: var(--bulma-primary-s);
  --bulma-tag-background-l: var(--bulma-primary-l);
  --bulma-tag-color-l: var(--bulma-primary-invert-l);
}
.bulma-tag.bulma-is-primary.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-primary-light-invert-l);
}
.bulma-tag.bulma-is-link {
  --bulma-tag-h: var(--bulma-link-h);
  --bulma-tag-s: var(--bulma-link-s);
  --bulma-tag-background-l: var(--bulma-link-l);
  --bulma-tag-color-l: var(--bulma-link-invert-l);
}
.bulma-tag.bulma-is-link.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-link-light-invert-l);
}
.bulma-tag.bulma-is-info {
  --bulma-tag-h: var(--bulma-info-h);
  --bulma-tag-s: var(--bulma-info-s);
  --bulma-tag-background-l: var(--bulma-info-l);
  --bulma-tag-color-l: var(--bulma-info-invert-l);
}
.bulma-tag.bulma-is-info.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-info-light-invert-l);
}
.bulma-tag.bulma-is-success {
  --bulma-tag-h: var(--bulma-success-h);
  --bulma-tag-s: var(--bulma-success-s);
  --bulma-tag-background-l: var(--bulma-success-l);
  --bulma-tag-color-l: var(--bulma-success-invert-l);
}
.bulma-tag.bulma-is-success.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-success-light-invert-l);
}
.bulma-tag.bulma-is-warning {
  --bulma-tag-h: var(--bulma-warning-h);
  --bulma-tag-s: var(--bulma-warning-s);
  --bulma-tag-background-l: var(--bulma-warning-l);
  --bulma-tag-color-l: var(--bulma-warning-invert-l);
}
.bulma-tag.bulma-is-warning.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-warning-light-invert-l);
}
.bulma-tag.bulma-is-danger {
  --bulma-tag-h: var(--bulma-danger-h);
  --bulma-tag-s: var(--bulma-danger-s);
  --bulma-tag-background-l: var(--bulma-danger-l);
  --bulma-tag-color-l: var(--bulma-danger-invert-l);
}
.bulma-tag.bulma-is-danger.bulma-is-light {
  --bulma-tag-background-l: var(--bulma-light-l);
  --bulma-tag-color-l: var(--bulma-danger-light-invert-l);
}
.bulma-tag.bulma-is-normal {
  font-size: var(--bulma-size-small);
}
.bulma-tag.bulma-is-medium {
  font-size: var(--bulma-size-normal);
}
.bulma-tag.bulma-is-large {
  font-size: var(--bulma-size-medium);
}
.bulma-tag .bulma-icon:first-child:not(:last-child) {
  margin-inline-start: -0.375em;
  margin-inline-end: 0.1875em;
}
.bulma-tag .bulma-icon:last-child:not(:first-child) {
  margin-inline-start: 0.1875em;
  margin-inline-end: -0.375em;
}
.bulma-tag .bulma-icon:first-child:last-child {
  margin-inline-start: -0.375em;
  margin-inline-end: -0.375em;
}
.bulma-tag.bulma-is-delete {
  margin-inline-start: var(--bulma-tag-delete-margin);
  padding: 0;
  position: relative;
  width: 2em;
}
.bulma-tag.bulma-is-delete::before, .bulma-tag.bulma-is-delete::after {
  background-color: currentColor;
  content: "";
  display: block;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(45deg);
  transform-origin: center center;
}
.bulma-tag.bulma-is-delete::before {
  height: 1px;
  width: 50%;
}
.bulma-tag.bulma-is-delete::after {
  height: 50%;
  width: 1px;
}
.bulma-tag.bulma-is-rounded {
  border-radius: var(--bulma-radius-rounded);
}

a.bulma-tag,
button.bulma-tag,
.bulma-tag.is-hoverable {
  cursor: pointer;
}
a.bulma-tag:hover,
button.bulma-tag:hover,
.bulma-tag.is-hoverable:hover {
  --bulma-tag-background-l-delta: var(--bulma-tag-hover-background-l-delta);
}
a.bulma-tag:active,
button.bulma-tag:active,
.bulma-tag.is-hoverable:active {
  --bulma-tag-background-l-delta: var(--bulma-tag-active-background-l-delta);
}

.bulma-title,
.bulma-subtitle {
  --bulma-title-color: var(--bulma-text-strong);
  --bulma-title-family: false;
  --bulma-title-size: var(--bulma-size-3);
  --bulma-title-weight: var(--bulma-weight-extrabold);
  --bulma-title-line-height: 1.125;
  --bulma-title-strong-color: inherit;
  --bulma-title-strong-weight: inherit;
  --bulma-title-sub-size: 0.75em;
  --bulma-title-sup-size: 0.75em;
  --bulma-subtitle-color: var(--bulma-text);
  --bulma-subtitle-family: false;
  --bulma-subtitle-size: var(--bulma-size-5);
  --bulma-subtitle-weight: var(--bulma-weight-normal);
  --bulma-subtitle-line-height: 1.25;
  --bulma-subtitle-strong-color: var(--bulma-text-strong);
  --bulma-subtitle-strong-weight: var(--bulma-weight-semibold);
}

.bulma-title,
.bulma-subtitle {
  word-break: break-word;
}
.bulma-title em,
.bulma-title span,
.bulma-subtitle em,
.bulma-subtitle span {
  font-weight: inherit;
}
.bulma-title sub,
.bulma-subtitle sub {
  font-size: var(--bulma-title-sub-size);
}
.bulma-title sup,
.bulma-subtitle sup {
  font-size: var(--bulma-title-sup-size);
}
.bulma-title .bulma-tag,
.bulma-subtitle .bulma-tag {
  vertical-align: middle;
}

.bulma-title {
  color: var(--bulma-title-color);
  font-size: var(--bulma-title-size);
  font-weight: var(--bulma-title-weight);
  line-height: var(--bulma-title-line-height);
}
.bulma-title strong {
  color: var(--bulma-title-strong-color);
  font-weight: var(--bulma-title-strong-weight);
}
.bulma-title:not(.bulma-is-spaced):has(+ .bulma-subtitle) {
  margin-bottom: 0;
}
.bulma-title.bulma-is-1 {
  font-size: 3rem;
}
.bulma-title.bulma-is-2 {
  font-size: 2.5rem;
}
.bulma-title.bulma-is-3 {
  font-size: 2rem;
}
.bulma-title.bulma-is-4 {
  font-size: 1.5rem;
}
.bulma-title.bulma-is-5 {
  font-size: 1.25rem;
}
.bulma-title.bulma-is-6 {
  font-size: 1rem;
}
.bulma-title.bulma-is-7 {
  font-size: 0.75rem;
}

.bulma-subtitle {
  color: var(--bulma-subtitle-color);
  font-size: var(--bulma-subtitle-size);
  font-weight: var(--bulma-subtitle-weight);
  line-height: var(--bulma-subtitle-line-height);
}
.bulma-subtitle strong {
  color: var(--bulma-subtitle-strong-color);
  font-weight: var(--bulma-subtitle-strong-weight);
}
.bulma-subtitle:not(.bulma-is-spaced):has(+ .bulma-title) {
  margin-bottom: 0;
}
.bulma-subtitle.bulma-is-1 {
  font-size: 3rem;
}
.bulma-subtitle.bulma-is-2 {
  font-size: 2.5rem;
}
.bulma-subtitle.bulma-is-3 {
  font-size: 2rem;
}
.bulma-subtitle.bulma-is-4 {
  font-size: 1.5rem;
}
.bulma-subtitle.bulma-is-5 {
  font-size: 1.25rem;
}
.bulma-subtitle.bulma-is-6 {
  font-size: 1rem;
}
.bulma-subtitle.bulma-is-7 {
  font-size: 0.75rem;
}

/* Bulma Form */
.bulma-control,
.bulma-input,
.bulma-textarea,
.bulma-select {
  --bulma-input-h: var(--bulma-scheme-h);
  --bulma-input-s: var(--bulma-scheme-s);
  --bulma-input-l: var(--bulma-scheme-main-l);
  --bulma-input-border-style: solid;
  --bulma-input-border-width: var(--bulma-control-border-width);
  --bulma-input-border-l: var(--bulma-border-l);
  --bulma-input-border-l-delta: 0%;
  --bulma-input-hover-border-l-delta: var(--bulma-hover-border-l-delta);
  --bulma-input-active-border-l-delta: var(--bulma-active-border-l-delta);
  --bulma-input-focus-h: var(--bulma-focus-h);
  --bulma-input-focus-s: var(--bulma-focus-s);
  --bulma-input-focus-l: var(--bulma-focus-l);
  --bulma-input-focus-shadow-size: var(--bulma-focus-shadow-size);
  --bulma-input-focus-shadow-alpha: var(--bulma-focus-shadow-alpha);
  --bulma-input-color-l: var(--bulma-text-strong-l);
  --bulma-input-background-l: var(--bulma-scheme-main-l);
  --bulma-input-background-l-delta: 0%;
  --bulma-input-height: var(--bulma-control-height);
  --bulma-input-shadow: inset 0 0.0625em 0.125em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.05);
  --bulma-input-placeholder-color: hsla(var(--bulma-text-h), var(--bulma-text-s), var(--bulma-text-strong-l), 0.3);
  --bulma-input-disabled-color: var(--bulma-text-weak);
  --bulma-input-disabled-background-color: var(--bulma-background);
  --bulma-input-disabled-border-color: var(--bulma-background);
  --bulma-input-disabled-placeholder-color: hsla(var(--bulma-text-h), var(--bulma-text-s), var(--bulma-text-weak-l), 0.3);
  --bulma-input-arrow: var(--bulma-link);
  --bulma-input-icon-color: var(--bulma-text-light);
  --bulma-input-icon-hover-color: var(--bulma-text-weak);
  --bulma-input-icon-focus-color: var(--bulma-link);
  --bulma-input-radius: var(--bulma-radius);
}

.bulma-select select, .bulma-input, .bulma-textarea {
  background-color: hsl(var(--bulma-input-h), var(--bulma-input-s), calc(var(--bulma-input-background-l) + var(--bulma-input-background-l-delta)));
  border-color: hsl(var(--bulma-input-h), var(--bulma-input-s), calc(var(--bulma-input-border-l) + var(--bulma-input-border-l-delta)));
  border-radius: var(--bulma-input-radius);
  color: hsl(var(--bulma-input-h), var(--bulma-input-s), var(--bulma-input-color-l));
}
.bulma-select select::-moz-placeholder, .bulma-input::-moz-placeholder, .bulma-textarea::-moz-placeholder {
  color: var(--bulma-input-placeholder-color);
}
.bulma-select select::-webkit-input-placeholder, .bulma-input::-webkit-input-placeholder, .bulma-textarea::-webkit-input-placeholder {
  color: var(--bulma-input-placeholder-color);
}
.bulma-select select:-moz-placeholder, .bulma-input:-moz-placeholder, .bulma-textarea:-moz-placeholder {
  color: var(--bulma-input-placeholder-color);
}
.bulma-select select:-ms-input-placeholder, .bulma-input:-ms-input-placeholder, .bulma-textarea:-ms-input-placeholder {
  color: var(--bulma-input-placeholder-color);
}
.bulma-select select:hover, .bulma-input:hover, .bulma-textarea:hover, .bulma-select select.bulma-is-hovered, .bulma-is-hovered.bulma-input, .bulma-is-hovered.bulma-textarea {
  --bulma-input-border-l-delta: var(--bulma-input-hover-border-l-delta);
}
.bulma-select select:active, .bulma-input:active, .bulma-textarea:active, .bulma-select select.bulma-is-active, .bulma-is-active.bulma-input, .bulma-is-active.bulma-textarea {
  --bulma-input-border-l-delta: var(--bulma-input-active-border-l-delta);
}
.bulma-select select:focus, .bulma-input:focus, .bulma-textarea:focus, .bulma-select select:focus-within, .bulma-input:focus-within, .bulma-textarea:focus-within, .bulma-select select.bulma-is-focused, .bulma-is-focused.bulma-input, .bulma-is-focused.bulma-textarea {
  border-color: hsl(var(--bulma-input-focus-h), var(--bulma-input-focus-s), var(--bulma-input-focus-l));
  box-shadow: var(--bulma-input-focus-shadow-size) hsla(var(--bulma-input-focus-h), var(--bulma-input-focus-s), var(--bulma-input-focus-l), var(--bulma-input-focus-shadow-alpha));
}
.bulma-select select[disabled], [disabled].bulma-input, [disabled].bulma-textarea, fieldset[disabled] .bulma-select select, .bulma-select fieldset[disabled] select, fieldset[disabled] .bulma-input, fieldset[disabled] .bulma-textarea {
  background-color: var(--bulma-input-disabled-background-color);
  border-color: var(--bulma-input-disabled-border-color);
  box-shadow: none;
  color: var(--bulma-input-disabled-color);
}
.bulma-select select[disabled]::-moz-placeholder, [disabled].bulma-input::-moz-placeholder, [disabled].bulma-textarea::-moz-placeholder, fieldset[disabled] .bulma-select select::-moz-placeholder, .bulma-select fieldset[disabled] select::-moz-placeholder, fieldset[disabled] .bulma-input::-moz-placeholder, fieldset[disabled] .bulma-textarea::-moz-placeholder {
  color: var(--bulma-input-disabled-placeholder-color);
}
.bulma-select select[disabled]::-webkit-input-placeholder, [disabled].bulma-input::-webkit-input-placeholder, [disabled].bulma-textarea::-webkit-input-placeholder, fieldset[disabled] .bulma-select select::-webkit-input-placeholder, .bulma-select fieldset[disabled] select::-webkit-input-placeholder, fieldset[disabled] .bulma-input::-webkit-input-placeholder, fieldset[disabled] .bulma-textarea::-webkit-input-placeholder {
  color: var(--bulma-input-disabled-placeholder-color);
}
.bulma-select select[disabled]:-moz-placeholder, [disabled].bulma-input:-moz-placeholder, [disabled].bulma-textarea:-moz-placeholder, fieldset[disabled] .bulma-select select:-moz-placeholder, .bulma-select fieldset[disabled] select:-moz-placeholder, fieldset[disabled] .bulma-input:-moz-placeholder, fieldset[disabled] .bulma-textarea:-moz-placeholder {
  color: var(--bulma-input-disabled-placeholder-color);
}
.bulma-select select[disabled]:-ms-input-placeholder, [disabled].bulma-input:-ms-input-placeholder, [disabled].bulma-textarea:-ms-input-placeholder, fieldset[disabled] .bulma-select select:-ms-input-placeholder, .bulma-select fieldset[disabled] select:-ms-input-placeholder, fieldset[disabled] .bulma-input:-ms-input-placeholder, fieldset[disabled] .bulma-textarea:-ms-input-placeholder {
  color: var(--bulma-input-disabled-placeholder-color);
}

/* Bulma Form */
.bulma-textarea, .bulma-input {
  box-shadow: inset 0 0.0625em 0.125em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.05);
  max-width: 100%;
  width: 100%;
}
[readonly].bulma-textarea, [readonly].bulma-input {
  box-shadow: none;
}
.bulma-is-white.bulma-textarea, .bulma-is-white.bulma-input {
  --bulma-input-h: var(--bulma-white-h);
  --bulma-input-s: var(--bulma-white-s);
  --bulma-input-l: var(--bulma-white-l);
  --bulma-input-focus-h: var(--bulma-white-h);
  --bulma-input-focus-s: var(--bulma-white-s);
  --bulma-input-focus-l: var(--bulma-white-l);
  --bulma-input-border-l: var(--bulma-white-l);
}
.bulma-is-black.bulma-textarea, .bulma-is-black.bulma-input {
  --bulma-input-h: var(--bulma-black-h);
  --bulma-input-s: var(--bulma-black-s);
  --bulma-input-l: var(--bulma-black-l);
  --bulma-input-focus-h: var(--bulma-black-h);
  --bulma-input-focus-s: var(--bulma-black-s);
  --bulma-input-focus-l: var(--bulma-black-l);
  --bulma-input-border-l: var(--bulma-black-l);
}
.bulma-is-light.bulma-textarea, .bulma-is-light.bulma-input {
  --bulma-input-h: var(--bulma-light-h);
  --bulma-input-s: var(--bulma-light-s);
  --bulma-input-l: var(--bulma-light-l);
  --bulma-input-focus-h: var(--bulma-light-h);
  --bulma-input-focus-s: var(--bulma-light-s);
  --bulma-input-focus-l: var(--bulma-light-l);
  --bulma-input-border-l: var(--bulma-light-l);
}
.bulma-is-dark.bulma-textarea, .bulma-is-dark.bulma-input {
  --bulma-input-h: var(--bulma-dark-h);
  --bulma-input-s: var(--bulma-dark-s);
  --bulma-input-l: var(--bulma-dark-l);
  --bulma-input-focus-h: var(--bulma-dark-h);
  --bulma-input-focus-s: var(--bulma-dark-s);
  --bulma-input-focus-l: var(--bulma-dark-l);
  --bulma-input-border-l: var(--bulma-dark-l);
}
.bulma-is-text.bulma-textarea, .bulma-is-text.bulma-input {
  --bulma-input-h: var(--bulma-text-h);
  --bulma-input-s: var(--bulma-text-s);
  --bulma-input-l: var(--bulma-text-l);
  --bulma-input-focus-h: var(--bulma-text-h);
  --bulma-input-focus-s: var(--bulma-text-s);
  --bulma-input-focus-l: var(--bulma-text-l);
  --bulma-input-border-l: var(--bulma-text-l);
}
.bulma-is-primary.bulma-textarea, .bulma-is-primary.bulma-input {
  --bulma-input-h: var(--bulma-primary-h);
  --bulma-input-s: var(--bulma-primary-s);
  --bulma-input-l: var(--bulma-primary-l);
  --bulma-input-focus-h: var(--bulma-primary-h);
  --bulma-input-focus-s: var(--bulma-primary-s);
  --bulma-input-focus-l: var(--bulma-primary-l);
  --bulma-input-border-l: var(--bulma-primary-l);
}
.bulma-is-link.bulma-textarea, .bulma-is-link.bulma-input {
  --bulma-input-h: var(--bulma-link-h);
  --bulma-input-s: var(--bulma-link-s);
  --bulma-input-l: var(--bulma-link-l);
  --bulma-input-focus-h: var(--bulma-link-h);
  --bulma-input-focus-s: var(--bulma-link-s);
  --bulma-input-focus-l: var(--bulma-link-l);
  --bulma-input-border-l: var(--bulma-link-l);
}
.bulma-is-info.bulma-textarea, .bulma-is-info.bulma-input {
  --bulma-input-h: var(--bulma-info-h);
  --bulma-input-s: var(--bulma-info-s);
  --bulma-input-l: var(--bulma-info-l);
  --bulma-input-focus-h: var(--bulma-info-h);
  --bulma-input-focus-s: var(--bulma-info-s);
  --bulma-input-focus-l: var(--bulma-info-l);
  --bulma-input-border-l: var(--bulma-info-l);
}
.bulma-is-success.bulma-textarea, .bulma-is-success.bulma-input {
  --bulma-input-h: var(--bulma-success-h);
  --bulma-input-s: var(--bulma-success-s);
  --bulma-input-l: var(--bulma-success-l);
  --bulma-input-focus-h: var(--bulma-success-h);
  --bulma-input-focus-s: var(--bulma-success-s);
  --bulma-input-focus-l: var(--bulma-success-l);
  --bulma-input-border-l: var(--bulma-success-l);
}
.bulma-is-warning.bulma-textarea, .bulma-is-warning.bulma-input {
  --bulma-input-h: var(--bulma-warning-h);
  --bulma-input-s: var(--bulma-warning-s);
  --bulma-input-l: var(--bulma-warning-l);
  --bulma-input-focus-h: var(--bulma-warning-h);
  --bulma-input-focus-s: var(--bulma-warning-s);
  --bulma-input-focus-l: var(--bulma-warning-l);
  --bulma-input-border-l: var(--bulma-warning-l);
}
.bulma-is-danger.bulma-textarea, .bulma-is-danger.bulma-input {
  --bulma-input-h: var(--bulma-danger-h);
  --bulma-input-s: var(--bulma-danger-s);
  --bulma-input-l: var(--bulma-danger-l);
  --bulma-input-focus-h: var(--bulma-danger-h);
  --bulma-input-focus-s: var(--bulma-danger-s);
  --bulma-input-focus-l: var(--bulma-danger-l);
  --bulma-input-border-l: var(--bulma-danger-l);
}
.bulma-is-small.bulma-textarea, .bulma-is-small.bulma-input {
  border-radius: var(--bulma-radius-small);
  font-size: var(--bulma-size-small);
}
.bulma-is-medium.bulma-textarea, .bulma-is-medium.bulma-input {
  font-size: var(--bulma-size-medium);
}
.bulma-is-large.bulma-textarea, .bulma-is-large.bulma-input {
  font-size: var(--bulma-size-large);
}
.bulma-is-fullwidth.bulma-textarea, .bulma-is-fullwidth.bulma-input {
  display: block;
  width: 100%;
}
.bulma-is-inline.bulma-textarea, .bulma-is-inline.bulma-input {
  display: inline;
  width: auto;
}

.bulma-input.bulma-is-rounded {
  border-radius: var(--bulma-radius-rounded);
  padding-left: calc(calc(0.75em - 1px) + 0.375em);
  padding-right: calc(calc(0.75em - 1px) + 0.375em);
}
.bulma-input.bulma-is-static {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
  padding-left: 0;
  padding-right: 0;
}

.bulma-textarea {
  --bulma-textarea-padding: var(--bulma-control-padding-horizontal);
  --bulma-textarea-max-height: 40em;
  --bulma-textarea-min-height: 8em;
  display: block;
  max-width: 100%;
  min-width: 100%;
  padding: var(--bulma-textarea-padding);
  resize: vertical;
}
.bulma-textarea:not([rows]) {
  max-height: var(--bulma-textarea-max-height);
  min-height: var(--bulma-textarea-min-height);
}
.bulma-textarea[rows] {
  height: initial;
}
.bulma-textarea.bulma-has-fixed-size {
  resize: none;
}

/* Bulma Form */
.bulma-radio, .bulma-checkbox {
  cursor: pointer;
  display: inline-block;
  line-height: 1.25;
  position: relative;
}
.bulma-radio input, .bulma-checkbox input {
  cursor: pointer;
}
[disabled].bulma-radio, [disabled].bulma-checkbox, fieldset[disabled] .bulma-radio, fieldset[disabled] .bulma-checkbox,
.bulma-radio input[disabled],
.bulma-checkbox input[disabled] {
  color: var(--bulma-text-weak);
  cursor: not-allowed;
}

.bulma-checkboxes,
.bulma-radios {
  display: flex;
  flex-wrap: wrap;
  column-gap: 1em;
  row-gap: 0.5em;
}

/* Bulma Form */
.bulma-select {
  --bulma-input-h: var(--bulma-scheme-h);
  --bulma-input-s: var(--bulma-scheme-s);
  --bulma-input-border-style: solid;
  --bulma-input-border-width: 1px;
  --bulma-input-border-l: var(--bulma-border-l);
  display: inline-block;
  max-width: 100%;
  position: relative;
  vertical-align: top;
}
.bulma-select:not(.bulma-is-multiple) {
  height: var(--bulma-control-height);
}
.bulma-select:not(.bulma-is-multiple):not(.bulma-is-loading)::after {
  inset-inline-end: 1.125em;
  z-index: 4;
}
.bulma-select.bulma-is-rounded select {
  border-radius: var(--bulma-radius-rounded);
  padding-inline-start: 1em;
}
.bulma-select select {
  cursor: pointer;
  display: block;
  font-size: 1em;
  max-width: 100%;
  outline: none;
}
.bulma-select select::-ms-expand {
  display: none;
}
.bulma-select select[disabled]:hover, fieldset[disabled] .bulma-select select:hover {
  border-color: var(--bulma-background);
}
.bulma-select select:not([multiple]) {
  padding-inline-end: 2.5em;
}
.bulma-select select[multiple] {
  height: auto;
  padding: 0;
}
.bulma-select select[multiple] option {
  padding: 0.5em 1em;
}
.bulma-select.bulma-is-white {
  --bulma-input-h: var(--bulma-white-h);
  --bulma-input-s: var(--bulma-white-s);
  --bulma-input-l: var(--bulma-white-l);
  --bulma-input-focus-h: var(--bulma-white-h);
  --bulma-input-focus-s: var(--bulma-white-s);
  --bulma-input-focus-l: var(--bulma-white-l);
  --bulma-input-border-l: var(--bulma-white-l);
  --bulma-arrow-color: var(--bulma-white);
}
.bulma-select.bulma-is-black {
  --bulma-input-h: var(--bulma-black-h);
  --bulma-input-s: var(--bulma-black-s);
  --bulma-input-l: var(--bulma-black-l);
  --bulma-input-focus-h: var(--bulma-black-h);
  --bulma-input-focus-s: var(--bulma-black-s);
  --bulma-input-focus-l: var(--bulma-black-l);
  --bulma-input-border-l: var(--bulma-black-l);
  --bulma-arrow-color: var(--bulma-black);
}
.bulma-select.bulma-is-light {
  --bulma-input-h: var(--bulma-light-h);
  --bulma-input-s: var(--bulma-light-s);
  --bulma-input-l: var(--bulma-light-l);
  --bulma-input-focus-h: var(--bulma-light-h);
  --bulma-input-focus-s: var(--bulma-light-s);
  --bulma-input-focus-l: var(--bulma-light-l);
  --bulma-input-border-l: var(--bulma-light-l);
  --bulma-arrow-color: var(--bulma-light);
}
.bulma-select.bulma-is-dark {
  --bulma-input-h: var(--bulma-dark-h);
  --bulma-input-s: var(--bulma-dark-s);
  --bulma-input-l: var(--bulma-dark-l);
  --bulma-input-focus-h: var(--bulma-dark-h);
  --bulma-input-focus-s: var(--bulma-dark-s);
  --bulma-input-focus-l: var(--bulma-dark-l);
  --bulma-input-border-l: var(--bulma-dark-l);
  --bulma-arrow-color: var(--bulma-dark);
}
.bulma-select.bulma-is-text {
  --bulma-input-h: var(--bulma-text-h);
  --bulma-input-s: var(--bulma-text-s);
  --bulma-input-l: var(--bulma-text-l);
  --bulma-input-focus-h: var(--bulma-text-h);
  --bulma-input-focus-s: var(--bulma-text-s);
  --bulma-input-focus-l: var(--bulma-text-l);
  --bulma-input-border-l: var(--bulma-text-l);
  --bulma-arrow-color: var(--bulma-text);
}
.bulma-select.bulma-is-primary {
  --bulma-input-h: var(--bulma-primary-h);
  --bulma-input-s: var(--bulma-primary-s);
  --bulma-input-l: var(--bulma-primary-l);
  --bulma-input-focus-h: var(--bulma-primary-h);
  --bulma-input-focus-s: var(--bulma-primary-s);
  --bulma-input-focus-l: var(--bulma-primary-l);
  --bulma-input-border-l: var(--bulma-primary-l);
  --bulma-arrow-color: var(--bulma-primary);
}
.bulma-select.bulma-is-link {
  --bulma-input-h: var(--bulma-link-h);
  --bulma-input-s: var(--bulma-link-s);
  --bulma-input-l: var(--bulma-link-l);
  --bulma-input-focus-h: var(--bulma-link-h);
  --bulma-input-focus-s: var(--bulma-link-s);
  --bulma-input-focus-l: var(--bulma-link-l);
  --bulma-input-border-l: var(--bulma-link-l);
  --bulma-arrow-color: var(--bulma-link);
}
.bulma-select.bulma-is-info {
  --bulma-input-h: var(--bulma-info-h);
  --bulma-input-s: var(--bulma-info-s);
  --bulma-input-l: var(--bulma-info-l);
  --bulma-input-focus-h: var(--bulma-info-h);
  --bulma-input-focus-s: var(--bulma-info-s);
  --bulma-input-focus-l: var(--bulma-info-l);
  --bulma-input-border-l: var(--bulma-info-l);
  --bulma-arrow-color: var(--bulma-info);
}
.bulma-select.bulma-is-success {
  --bulma-input-h: var(--bulma-success-h);
  --bulma-input-s: var(--bulma-success-s);
  --bulma-input-l: var(--bulma-success-l);
  --bulma-input-focus-h: var(--bulma-success-h);
  --bulma-input-focus-s: var(--bulma-success-s);
  --bulma-input-focus-l: var(--bulma-success-l);
  --bulma-input-border-l: var(--bulma-success-l);
  --bulma-arrow-color: var(--bulma-success);
}
.bulma-select.bulma-is-warning {
  --bulma-input-h: var(--bulma-warning-h);
  --bulma-input-s: var(--bulma-warning-s);
  --bulma-input-l: var(--bulma-warning-l);
  --bulma-input-focus-h: var(--bulma-warning-h);
  --bulma-input-focus-s: var(--bulma-warning-s);
  --bulma-input-focus-l: var(--bulma-warning-l);
  --bulma-input-border-l: var(--bulma-warning-l);
  --bulma-arrow-color: var(--bulma-warning);
}
.bulma-select.bulma-is-danger {
  --bulma-input-h: var(--bulma-danger-h);
  --bulma-input-s: var(--bulma-danger-s);
  --bulma-input-l: var(--bulma-danger-l);
  --bulma-input-focus-h: var(--bulma-danger-h);
  --bulma-input-focus-s: var(--bulma-danger-s);
  --bulma-input-focus-l: var(--bulma-danger-l);
  --bulma-input-border-l: var(--bulma-danger-l);
  --bulma-arrow-color: var(--bulma-danger);
}
.bulma-select.bulma-is-small {
  border-radius: var(--bulma-radius-small);
  font-size: var(--bulma-size-small);
}
.bulma-select.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-select.bulma-is-large {
  font-size: var(--bulma-size-large);
}
.bulma-select.bulma-is-disabled::after {
  border-color: var(--bulma-text-weak) !important;
  opacity: 0.5;
}
.bulma-select.bulma-is-fullwidth {
  width: 100%;
}
.bulma-select.bulma-is-fullwidth select {
  width: 100%;
}
.bulma-select.bulma-is-loading::after {
  inset-inline-end: 0.625em;
  margin-top: 0;
  position: absolute;
  top: 0.625em;
  transform: none;
}
.bulma-select.bulma-is-loading.bulma-is-small:after {
  font-size: var(--bulma-size-small);
}
.bulma-select.bulma-is-loading.bulma-is-medium:after {
  font-size: var(--bulma-size-medium);
}
.bulma-select.bulma-is-loading.bulma-is-large:after {
  font-size: var(--bulma-size-large);
}

/* Bulma Form */
.bulma-file {
  --bulma-file-radius: var(--bulma-radius);
  --bulma-file-name-border-color: var(--bulma-border);
  --bulma-file-name-border-style: solid;
  --bulma-file-name-border-width: 1px 1px 1px 0;
  --bulma-file-name-max-width: 16em;
  --bulma-file-h: var(--bulma-scheme-h);
  --bulma-file-s: var(--bulma-scheme-s);
  --bulma-file-background-l: var(--bulma-scheme-main-ter-l);
  --bulma-file-background-l-delta: 0%;
  --bulma-file-hover-background-l-delta: -5%;
  --bulma-file-active-background-l-delta: -10%;
  --bulma-file-border-l: var(--bulma-border-l);
  --bulma-file-border-l-delta: 0%;
  --bulma-file-hover-border-l-delta: -10%;
  --bulma-file-active-border-l-delta: -20%;
  --bulma-file-cta-color-l: var(--bulma-text-strong-l);
  --bulma-file-name-color-l: var(--bulma-text-strong-l);
  --bulma-file-color-l-delta: 0%;
  --bulma-file-hover-color-l-delta: -5%;
  --bulma-file-active-color-l-delta: -10%;
  align-items: stretch;
  display: flex;
  justify-content: flex-start;
  position: relative;
}
.bulma-file.bulma-is-white {
  --bulma-file-h: var(--bulma-white-h);
  --bulma-file-s: var(--bulma-white-s);
  --bulma-file-background-l: var(--bulma-white-l);
  --bulma-file-border-l: var(--bulma-white-l);
  --bulma-file-cta-color-l: var(--bulma-white-invert-l);
  --bulma-file-name-color-l: var(--bulma-white-on-scheme-l);
}
.bulma-file.bulma-is-black {
  --bulma-file-h: var(--bulma-black-h);
  --bulma-file-s: var(--bulma-black-s);
  --bulma-file-background-l: var(--bulma-black-l);
  --bulma-file-border-l: var(--bulma-black-l);
  --bulma-file-cta-color-l: var(--bulma-black-invert-l);
  --bulma-file-name-color-l: var(--bulma-black-on-scheme-l);
}
.bulma-file.bulma-is-light {
  --bulma-file-h: var(--bulma-light-h);
  --bulma-file-s: var(--bulma-light-s);
  --bulma-file-background-l: var(--bulma-light-l);
  --bulma-file-border-l: var(--bulma-light-l);
  --bulma-file-cta-color-l: var(--bulma-light-invert-l);
  --bulma-file-name-color-l: var(--bulma-light-on-scheme-l);
}
.bulma-file.bulma-is-dark {
  --bulma-file-h: var(--bulma-dark-h);
  --bulma-file-s: var(--bulma-dark-s);
  --bulma-file-background-l: var(--bulma-dark-l);
  --bulma-file-border-l: var(--bulma-dark-l);
  --bulma-file-cta-color-l: var(--bulma-dark-invert-l);
  --bulma-file-name-color-l: var(--bulma-dark-on-scheme-l);
}
.bulma-file.bulma-is-text {
  --bulma-file-h: var(--bulma-text-h);
  --bulma-file-s: var(--bulma-text-s);
  --bulma-file-background-l: var(--bulma-text-l);
  --bulma-file-border-l: var(--bulma-text-l);
  --bulma-file-cta-color-l: var(--bulma-text-invert-l);
  --bulma-file-name-color-l: var(--bulma-text-on-scheme-l);
}
.bulma-file.bulma-is-primary {
  --bulma-file-h: var(--bulma-primary-h);
  --bulma-file-s: var(--bulma-primary-s);
  --bulma-file-background-l: var(--bulma-primary-l);
  --bulma-file-border-l: var(--bulma-primary-l);
  --bulma-file-cta-color-l: var(--bulma-primary-invert-l);
  --bulma-file-name-color-l: var(--bulma-primary-on-scheme-l);
}
.bulma-file.bulma-is-link {
  --bulma-file-h: var(--bulma-link-h);
  --bulma-file-s: var(--bulma-link-s);
  --bulma-file-background-l: var(--bulma-link-l);
  --bulma-file-border-l: var(--bulma-link-l);
  --bulma-file-cta-color-l: var(--bulma-link-invert-l);
  --bulma-file-name-color-l: var(--bulma-link-on-scheme-l);
}
.bulma-file.bulma-is-info {
  --bulma-file-h: var(--bulma-info-h);
  --bulma-file-s: var(--bulma-info-s);
  --bulma-file-background-l: var(--bulma-info-l);
  --bulma-file-border-l: var(--bulma-info-l);
  --bulma-file-cta-color-l: var(--bulma-info-invert-l);
  --bulma-file-name-color-l: var(--bulma-info-on-scheme-l);
}
.bulma-file.bulma-is-success {
  --bulma-file-h: var(--bulma-success-h);
  --bulma-file-s: var(--bulma-success-s);
  --bulma-file-background-l: var(--bulma-success-l);
  --bulma-file-border-l: var(--bulma-success-l);
  --bulma-file-cta-color-l: var(--bulma-success-invert-l);
  --bulma-file-name-color-l: var(--bulma-success-on-scheme-l);
}
.bulma-file.bulma-is-warning {
  --bulma-file-h: var(--bulma-warning-h);
  --bulma-file-s: var(--bulma-warning-s);
  --bulma-file-background-l: var(--bulma-warning-l);
  --bulma-file-border-l: var(--bulma-warning-l);
  --bulma-file-cta-color-l: var(--bulma-warning-invert-l);
  --bulma-file-name-color-l: var(--bulma-warning-on-scheme-l);
}
.bulma-file.bulma-is-danger {
  --bulma-file-h: var(--bulma-danger-h);
  --bulma-file-s: var(--bulma-danger-s);
  --bulma-file-background-l: var(--bulma-danger-l);
  --bulma-file-border-l: var(--bulma-danger-l);
  --bulma-file-cta-color-l: var(--bulma-danger-invert-l);
  --bulma-file-name-color-l: var(--bulma-danger-on-scheme-l);
}
.bulma-file.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-file.bulma-is-normal {
  font-size: var(--bulma-size-normal);
}
.bulma-file.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-file.bulma-is-medium .bulma-file-icon .bulma-fa {
  font-size: 1.5rem;
}
.bulma-file.bulma-is-large {
  font-size: var(--bulma-size-large);
}
.bulma-file.bulma-is-large .bulma-file-icon .bulma-fa {
  font-size: 2rem;
}
.bulma-file.bulma-has-name .bulma-file-cta {
  border-end-end-radius: 0;
  border-start-end-radius: 0;
}
.bulma-file.bulma-has-name .bulma-file-name {
  border-end-start-radius: 0;
  border-start-start-radius: 0;
}
.bulma-file.bulma-has-name.bulma-is-empty .bulma-file-cta {
  border-radius: var(--bulma-file-radius);
}
.bulma-file.bulma-has-name.bulma-is-empty .bulma-file-name {
  display: none;
}
.bulma-file.bulma-is-boxed .bulma-file-label {
  flex-direction: column;
}
.bulma-file.bulma-is-boxed .bulma-file-cta {
  flex-direction: column;
  height: auto;
  padding: 1em 3em;
}
.bulma-file.bulma-is-boxed .bulma-file-name {
  border-width: 0 1px 1px;
}
.bulma-file.bulma-is-boxed .bulma-file-icon {
  height: 1.5em;
  width: 1.5em;
}
.bulma-file.bulma-is-boxed .bulma-file-icon .bulma-fa {
  font-size: 1.5rem;
}
.bulma-file.bulma-is-boxed.bulma-is-small .bulma-file-icon .bulma-fa {
  font-size: 1rem;
}
.bulma-file.bulma-is-boxed.bulma-is-medium .bulma-file-icon .bulma-fa {
  font-size: 2rem;
}
.bulma-file.bulma-is-boxed.bulma-is-large .bulma-file-icon .bulma-fa {
  font-size: 2.5rem;
}
.bulma-file.bulma-is-boxed.bulma-has-name .bulma-file-cta {
  border-end-end-radius: 0;
  border-end-start-radius: 0;
  border-start-end-radius: var(--bulma-file-radius);
  border-start-start-radius: var(--bulma-file-radius);
}
.bulma-file.bulma-is-boxed.bulma-has-name .bulma-file-name {
  border-end-end-radius: var(--bulma-file-radius);
  border-end-start-radius: var(--bulma-file-radius);
  border-start-end-radius: 0;
  border-start-start-radius: 0;
  border-width: 0 1px 1px;
}
.bulma-file.bulma-is-centered {
  justify-content: center;
}
.bulma-file.bulma-is-fullwidth .bulma-file-label {
  width: 100%;
}
.bulma-file.bulma-is-fullwidth .bulma-file-name {
  flex-grow: 1;
  max-width: none;
}
.bulma-file.bulma-is-right {
  justify-content: flex-end;
}
.bulma-file.bulma-is-right .bulma-file-cta {
  border-radius: 0 var(--bulma-file-radius) var(--bulma-file-radius) 0;
}
.bulma-file.bulma-is-right .bulma-file-name {
  border-radius: var(--bulma-file-radius) 0 0 var(--bulma-file-radius);
  border-width: 1px 0 1px 1px;
  order: -1;
}

.bulma-file-label {
  align-items: stretch;
  display: flex;
  cursor: pointer;
  justify-content: flex-start;
  overflow: hidden;
  position: relative;
}
.bulma-file-label:hover {
  --bulma-file-background-l-delta: var(--bulma-file-hover-background-l-delta);
  --bulma-file-border-l-delta: var(--bulma-file-hover-border-l-delta);
  --bulma-file-color-l-delta: var(--bulma-file-hover-color-l-delta);
}
.bulma-file-label:active {
  --bulma-file-background-l-delta: var(--bulma-file-active-background-l-delta);
  --bulma-file-border-l-delta: var(--bulma-file-active-border-l-delta);
  --bulma-file-color-l-delta: var(--bulma-file-active-color-l-delta);
}

.bulma-file-input {
  height: 100%;
  left: 0;
  opacity: 0;
  outline: none;
  position: absolute;
  top: 0;
  width: 100%;
}

.bulma-file-cta,
.bulma-file-name {
  border-color: hsl(var(--bulma-file-h), var(--bulma-file-s), calc(var(--bulma-file-border-l) + var(--bulma-file-border-l-delta)));
  border-radius: var(--bulma-file-radius);
  font-size: 1em;
  padding-left: 1em;
  padding-right: 1em;
  white-space: nowrap;
}

.bulma-file-cta {
  background-color: hsl(var(--bulma-file-h), var(--bulma-file-s), calc(var(--bulma-file-background-l) + var(--bulma-file-background-l-delta)));
  color: hsl(var(--bulma-file-h), var(--bulma-file-s), calc(var(--bulma-file-cta-color-l) + var(--bulma-file-color-l-delta)));
}

.bulma-file-name {
  border-color: hsl(var(--bulma-file-h), var(--bulma-file-s), calc(var(--bulma-file-border-l) + var(--bulma-file-color-l-delta)));
  border-style: var(--bulma-file-name-border-style);
  border-width: var(--bulma-file-name-border-width);
  color: hsl(var(--bulma-file-h), var(--bulma-file-s), calc(var(--bulma-file-name-color-l) + var(--bulma-file-color-l-delta)));
  display: block;
  max-width: var(--bulma-file-name-max-width);
  overflow: hidden;
  text-align: inherit;
  text-overflow: ellipsis;
}

.bulma-file-icon {
  align-items: center;
  display: flex;
  height: 1em;
  justify-content: center;
  margin-inline-end: 0.5em;
  width: 1em;
}
.bulma-file-icon .bulma-fa {
  font-size: 1rem;
}

/* Bulma Form */
:root {
  --bulma-label-color: var(--bulma-text-strong);
  --bulma-label-spacing: 0.5em;
  --bulma-label-weight: var(--bulma-weight-semibold);
  --bulma-help-size: var(--bulma-size-small);
  --bulma-field-block-spacing: 0.75rem;
}

.bulma-label {
  color: var(--bulma-label-color);
  display: block;
  font-size: var(--bulma-size-normal);
  font-weight: var(--bulma-weight-semibold);
}
.bulma-label:not(:last-child) {
  margin-bottom: var(--bulma-label-spacing);
}
.bulma-label.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-label.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-label.bulma-is-large {
  font-size: var(--bulma-size-large);
}

.bulma-help {
  display: block;
  font-size: var(--bulma-help-size);
  margin-top: 0.25rem;
}
.bulma-help.bulma-is-white {
  color: hsl(var(--bulma-white-h), var(--bulma-white-s), var(--bulma-white-on-scheme-l));
}
.bulma-help.bulma-is-black {
  color: hsl(var(--bulma-black-h), var(--bulma-black-s), var(--bulma-black-on-scheme-l));
}
.bulma-help.bulma-is-light {
  color: hsl(var(--bulma-light-h), var(--bulma-light-s), var(--bulma-light-on-scheme-l));
}
.bulma-help.bulma-is-dark {
  color: hsl(var(--bulma-dark-h), var(--bulma-dark-s), var(--bulma-dark-on-scheme-l));
}
.bulma-help.bulma-is-text {
  color: hsl(var(--bulma-text-h), var(--bulma-text-s), var(--bulma-text-on-scheme-l));
}
.bulma-help.bulma-is-primary {
  color: hsl(var(--bulma-primary-h), var(--bulma-primary-s), var(--bulma-primary-on-scheme-l));
}
.bulma-help.bulma-is-link {
  color: hsl(var(--bulma-link-h), var(--bulma-link-s), var(--bulma-link-on-scheme-l));
}
.bulma-help.bulma-is-info {
  color: hsl(var(--bulma-info-h), var(--bulma-info-s), var(--bulma-info-on-scheme-l));
}
.bulma-help.bulma-is-success {
  color: hsl(var(--bulma-success-h), var(--bulma-success-s), var(--bulma-success-on-scheme-l));
}
.bulma-help.bulma-is-warning {
  color: hsl(var(--bulma-warning-h), var(--bulma-warning-s), var(--bulma-warning-on-scheme-l));
}
.bulma-help.bulma-is-danger {
  color: hsl(var(--bulma-danger-h), var(--bulma-danger-s), var(--bulma-danger-on-scheme-l));
}

.bulma-field {
  --bulma-block-spacing: var(--bulma-field-block-spacing);
}
.bulma-field.bulma-has-addons {
  display: flex;
  justify-content: flex-start;
}
.bulma-field.bulma-has-addons .bulma-control:not(:last-child) {
  margin-inline-end: -1px;
}
.bulma-field.bulma-has-addons .bulma-control:not(:first-child):not(:last-child) .bulma-button,
.bulma-field.bulma-has-addons .bulma-control:not(:first-child):not(:last-child) .bulma-input,
.bulma-field.bulma-has-addons .bulma-control:not(:first-child):not(:last-child) .bulma-select select {
  border-radius: 0;
}
.bulma-field.bulma-has-addons .bulma-control:first-child:not(:only-child) .bulma-button,
.bulma-field.bulma-has-addons .bulma-control:first-child:not(:only-child) .bulma-input,
.bulma-field.bulma-has-addons .bulma-control:first-child:not(:only-child) .bulma-select select {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
}
.bulma-field.bulma-has-addons .bulma-control:last-child:not(:only-child) .bulma-button,
.bulma-field.bulma-has-addons .bulma-control:last-child:not(:only-child) .bulma-input,
.bulma-field.bulma-has-addons .bulma-control:last-child:not(:only-child) .bulma-select select {
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}
.bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]):hover, .bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]).bulma-is-hovered,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]):hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]).bulma-is-hovered,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]):hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]).bulma-is-hovered {
  z-index: 2;
}
.bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]):focus, .bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]).bulma-is-focused, .bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]):active, .bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]).bulma-is-active,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]):focus,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]).bulma-is-focused,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]):active,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]).bulma-is-active,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]):focus,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]).bulma-is-focused,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]):active,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]).bulma-is-active {
  z-index: 3;
}
.bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]):focus:hover, .bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]).bulma-is-focused:hover, .bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]):active:hover, .bulma-field.bulma-has-addons .bulma-control .bulma-button:not([disabled]).bulma-is-active:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]):focus:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]).bulma-is-focused:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]):active:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-input:not([disabled]).bulma-is-active:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]):focus:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]).bulma-is-focused:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]):active:hover,
.bulma-field.bulma-has-addons .bulma-control .bulma-select select:not([disabled]).bulma-is-active:hover {
  z-index: 4;
}
.bulma-field.bulma-has-addons .bulma-control.bulma-is-expanded {
  flex-grow: 1;
  flex-shrink: 1;
}
.bulma-field.bulma-has-addons.bulma-has-addons-centered {
  justify-content: center;
}
.bulma-field.bulma-has-addons.bulma-has-addons-right {
  justify-content: flex-end;
}
.bulma-field.bulma-has-addons.bulma-has-addons-fullwidth .bulma-control {
  flex-grow: 1;
  flex-shrink: 0;
}
.bulma-field.bulma-is-grouped {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-start;
}
.bulma-field.bulma-is-grouped > .bulma-control {
  flex-shrink: 0;
}
.bulma-field.bulma-is-grouped > .bulma-control.bulma-is-expanded {
  flex-grow: 1;
  flex-shrink: 1;
}
.bulma-field.bulma-is-grouped.bulma-is-grouped-centered {
  justify-content: center;
}
.bulma-field.bulma-is-grouped.bulma-is-grouped-right {
  justify-content: flex-end;
}
.bulma-field.bulma-is-grouped.bulma-is-grouped-multiline {
  flex-wrap: wrap;
}
@media screen and (min-width: 769px), print {
  .bulma-field.bulma-is-horizontal {
    display: flex;
  }
}

.bulma-field-label .bulma-label {
  font-size: inherit;
}
@media screen and (max-width: 768px) {
  .bulma-field-label {
    margin-bottom: 0.5rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-field-label {
    flex-basis: 0;
    flex-grow: 1;
    flex-shrink: 0;
    margin-inline-end: 1.5rem;
    text-align: right;
  }
  .bulma-field-label.bulma-is-small {
    font-size: var(--bulma-size-small);
    padding-top: 0.375em;
  }
  .bulma-field-label.bulma-is-normal {
    padding-top: 0.375em;
  }
  .bulma-field-label.bulma-is-medium {
    font-size: var(--bulma-size-medium);
    padding-top: 0.375em;
  }
  .bulma-field-label.bulma-is-large {
    font-size: var(--bulma-size-large);
    padding-top: 0.375em;
  }
}

.bulma-field-body .bulma-field .bulma-field {
  margin-bottom: 0;
}
@media screen and (min-width: 769px), print {
  .bulma-field-body {
    display: flex;
    flex-basis: 0;
    flex-grow: 5;
    flex-shrink: 1;
  }
  .bulma-field-body .bulma-field {
    margin-bottom: 0;
  }
  .bulma-field-body > .bulma-field {
    flex-shrink: 1;
  }
  .bulma-field-body > .bulma-field:not(.bulma-is-narrow) {
    flex-grow: 1;
  }
  .bulma-field-body > .bulma-field:not(:last-child) {
    margin-inline-end: 0.75rem;
  }
}

.bulma-control {
  box-sizing: border-box;
  clear: both;
  font-size: var(--bulma-size-normal);
  position: relative;
  text-align: inherit;
}
.bulma-control.bulma-has-icons-left .bulma-input:hover ~ .bulma-icon,
.bulma-control.bulma-has-icons-left .bulma-select:hover ~ .bulma-icon, .bulma-control.bulma-has-icons-right .bulma-input:hover ~ .bulma-icon,
.bulma-control.bulma-has-icons-right .bulma-select:hover ~ .bulma-icon {
  color: var(--bulma-input-icon-hover-color);
}
.bulma-control.bulma-has-icons-left .bulma-input:focus ~ .bulma-icon,
.bulma-control.bulma-has-icons-left .bulma-select:focus ~ .bulma-icon, .bulma-control.bulma-has-icons-right .bulma-input:focus ~ .bulma-icon,
.bulma-control.bulma-has-icons-right .bulma-select:focus ~ .bulma-icon {
  color: var(--bulma-input-icon-focus-color);
}
.bulma-control.bulma-has-icons-left .bulma-input.bulma-is-small ~ .bulma-icon,
.bulma-control.bulma-has-icons-left .bulma-select.bulma-is-small ~ .bulma-icon, .bulma-control.bulma-has-icons-right .bulma-input.bulma-is-small ~ .bulma-icon,
.bulma-control.bulma-has-icons-right .bulma-select.bulma-is-small ~ .bulma-icon {
  font-size: var(--bulma-size-small);
}
.bulma-control.bulma-has-icons-left .bulma-input.bulma-is-medium ~ .bulma-icon,
.bulma-control.bulma-has-icons-left .bulma-select.bulma-is-medium ~ .bulma-icon, .bulma-control.bulma-has-icons-right .bulma-input.bulma-is-medium ~ .bulma-icon,
.bulma-control.bulma-has-icons-right .bulma-select.bulma-is-medium ~ .bulma-icon {
  font-size: var(--bulma-size-medium);
}
.bulma-control.bulma-has-icons-left .bulma-input.bulma-is-large ~ .bulma-icon,
.bulma-control.bulma-has-icons-left .bulma-select.bulma-is-large ~ .bulma-icon, .bulma-control.bulma-has-icons-right .bulma-input.bulma-is-large ~ .bulma-icon,
.bulma-control.bulma-has-icons-right .bulma-select.bulma-is-large ~ .bulma-icon {
  font-size: var(--bulma-size-large);
}
.bulma-control.bulma-has-icons-left .bulma-icon, .bulma-control.bulma-has-icons-right .bulma-icon {
  color: var(--bulma-input-icon-color);
  height: var(--bulma-input-height);
  pointer-events: none;
  position: absolute;
  top: 0;
  width: var(--bulma-input-height);
  z-index: 4;
}
.bulma-control.bulma-has-icons-left .bulma-input,
.bulma-control.bulma-has-icons-left .bulma-select select {
  padding-left: var(--bulma-input-height);
}
.bulma-control.bulma-has-icons-left .bulma-icon.bulma-is-left {
  left: 0;
}
.bulma-control.bulma-has-icons-right .bulma-input,
.bulma-control.bulma-has-icons-right .bulma-select select {
  padding-right: var(--bulma-input-height);
}
.bulma-control.bulma-has-icons-right .bulma-icon.bulma-is-right {
  right: 0;
}
.bulma-control.bulma-is-loading::after {
  inset-inline-end: 0.75em;
  position: absolute !important;
  top: 0.75em;
  z-index: 4;
}
.bulma-control.bulma-is-loading.bulma-is-small:after {
  font-size: var(--bulma-size-small);
}
.bulma-control.bulma-is-loading.bulma-is-medium:after {
  font-size: var(--bulma-size-medium);
}
.bulma-control.bulma-is-loading.bulma-is-large:after {
  font-size: var(--bulma-size-large);
}

/* Bulma Components */
.bulma-breadcrumb {
  --bulma-breadcrumb-item-color: var(--bulma-link-text);
  --bulma-breadcrumb-item-hover-color: var(--bulma-link-text-hover);
  --bulma-breadcrumb-item-active-color: var(--bulma-link-text-active);
  --bulma-breadcrumb-item-padding-vertical: 0;
  --bulma-breadcrumb-item-padding-horizontal: 0.75em;
  --bulma-breadcrumb-item-separator-color: var(--bulma-border);
}

.bulma-breadcrumb {
  font-size: var(--bulma-size-normal);
  white-space: nowrap;
}
.bulma-breadcrumb a {
  align-items: center;
  color: var(--bulma-breadcrumb-item-color);
  display: flex;
  justify-content: center;
  padding: var(--bulma-breadcrumb-item-padding-vertical) var(--bulma-breadcrumb-item-padding-horizontal);
}
.bulma-breadcrumb a:hover {
  color: var(--bulma-breadcrumb-item-hover-color);
}
.bulma-breadcrumb li {
  align-items: center;
  display: flex;
}
.bulma-breadcrumb li:first-child a {
  padding-inline-start: 0;
}
.bulma-breadcrumb li.bulma-is-active a {
  color: var(--bulma-breadcrumb-item-active-color);
  cursor: default;
  pointer-events: none;
}
.bulma-breadcrumb li + li::before {
  color: var(--bulma-breadcrumb-item-separator-color);
  content: "/";
}
.bulma-breadcrumb ul,
.bulma-breadcrumb ol {
  align-items: flex-start;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.bulma-breadcrumb .bulma-icon:first-child {
  margin-inline-end: 0.5em;
}
.bulma-breadcrumb .bulma-icon:last-child {
  margin-inline-start: 0.5em;
}
.bulma-breadcrumb.bulma-is-centered ol,
.bulma-breadcrumb.bulma-is-centered ul {
  justify-content: center;
}
.bulma-breadcrumb.bulma-is-right ol,
.bulma-breadcrumb.bulma-is-right ul {
  justify-content: flex-end;
}
.bulma-breadcrumb.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-breadcrumb.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-breadcrumb.bulma-is-large {
  font-size: var(--bulma-size-large);
}
.bulma-breadcrumb.bulma-has-arrow-separator li + li::before {
  content: "→";
}
.bulma-breadcrumb.bulma-has-bullet-separator li + li::before {
  content: "•";
}
.bulma-breadcrumb.bulma-has-dot-separator li + li::before {
  content: "·";
}
.bulma-breadcrumb.bulma-has-succeeds-separator li + li::before {
  content: "≻";
}

.bulma-card {
  --bulma-card-color: var(--bulma-text);
  --bulma-card-background-color: var(--bulma-scheme-main);
  --bulma-card-shadow: var(--bulma-shadow);
  --bulma-card-radius: 0.75rem;
  --bulma-card-header-background-color: transparent;
  --bulma-card-header-color: var(--bulma-text-strong);
  --bulma-card-header-padding: 0.75rem 1rem;
  --bulma-card-header-shadow: 0 0.125em 0.25em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1);
  --bulma-card-header-weight: var(--bulma-weight-bold);
  --bulma-card-content-background-color: transparent;
  --bulma-card-content-padding: 1.5rem;
  --bulma-card-footer-background-color: transparent;
  --bulma-card-footer-border-top: 1px solid var(--bulma-border-weak);
  --bulma-card-footer-padding: 0.75rem;
  --bulma-card-media-margin: var(--bulma-block-spacing);
}

.bulma-card {
  background-color: var(--bulma-card-background-color);
  border-radius: var(--bulma-card-radius);
  box-shadow: var(--bulma-card-shadow);
  color: var(--bulma-card-color);
  max-width: 100%;
  position: relative;
}

.bulma-card-footer:first-child, .bulma-card-content:first-child, .bulma-card-header:first-child {
  border-start-start-radius: var(--bulma-card-radius);
  border-start-end-radius: var(--bulma-card-radius);
}
.bulma-card-footer:last-child, .bulma-card-content:last-child, .bulma-card-header:last-child {
  border-end-start-radius: var(--bulma-card-radius);
  border-end-end-radius: var(--bulma-card-radius);
}

.bulma-card-header {
  background-color: var(--bulma-card-header-background-color);
  align-items: stretch;
  box-shadow: var(--bulma-card-header-shadow);
  display: flex;
}

.bulma-card-header-title {
  align-items: center;
  color: var(--bulma-card-header-color);
  display: flex;
  flex-grow: 1;
  font-weight: var(--bulma-card-header-weight);
  padding: var(--bulma-card-header-padding);
}
.bulma-card-header-title.bulma-is-centered {
  justify-content: center;
}

.bulma-card-header-icon {
  appearance: none;
  background: none;
  border: none;
  color: inherit;
  font-family: inherit;
  font-size: 1em;
  margin: 0;
  padding: 0;
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: center;
  padding: var(--bulma-card-header-padding);
}

.bulma-card-image {
  display: block;
  position: relative;
}
.bulma-card-image:first-child img {
  border-start-start-radius: var(--bulma-card-radius);
  border-start-end-radius: var(--bulma-card-radius);
}
.bulma-card-image:last-child img {
  border-end-start-radius: var(--bulma-card-radius);
  border-end-end-radius: var(--bulma-card-radius);
}

.bulma-card-content {
  background-color: var(--bulma-card-content-background-color);
  padding: var(--bulma-card-content-padding);
}

.bulma-card-footer {
  background-color: var(--bulma-card-footer-background-color);
  border-top: var(--bulma-card-footer-border-top);
  align-items: stretch;
  display: flex;
}

.bulma-card-footer-item {
  align-items: center;
  display: flex;
  flex-basis: 0;
  flex-grow: 1;
  flex-shrink: 0;
  justify-content: center;
  padding: var(--bulma-card-footer-padding);
}
.bulma-card-footer-item:not(:last-child) {
  border-inline-end: var(--bulma-card-footer-border-top);
}

.bulma-card .bulma-media:not(:last-child) {
  margin-bottom: var(--bulma-card-media-margin);
}

.bulma-dropdown {
  --bulma-dropdown-menu-min-width: 12rem;
  --bulma-dropdown-content-background-color: var(--bulma-scheme-main);
  --bulma-dropdown-content-offset: 0.25rem;
  --bulma-dropdown-content-padding-bottom: 0.5rem;
  --bulma-dropdown-content-padding-top: 0.5rem;
  --bulma-dropdown-content-radius: var(--bulma-radius);
  --bulma-dropdown-content-shadow: var(--bulma-shadow);
  --bulma-dropdown-content-z: 20;
  --bulma-dropdown-item-h: var(--bulma-scheme-h);
  --bulma-dropdown-item-s: var(--bulma-scheme-s);
  --bulma-dropdown-item-l: var(--bulma-scheme-main-l);
  --bulma-dropdown-item-background-l: var(--bulma-scheme-main-l);
  --bulma-dropdown-item-background-l-delta: 0%;
  --bulma-dropdown-item-hover-background-l-delta: var(--bulma-hover-background-l-delta);
  --bulma-dropdown-item-active-background-l-delta: var(--bulma-active-background-l-delta);
  --bulma-dropdown-item-color-l: var(--bulma-text-strong-l);
  --bulma-dropdown-item-selected-h: var(--bulma-link-h);
  --bulma-dropdown-item-selected-s: var(--bulma-link-s);
  --bulma-dropdown-item-selected-l: var(--bulma-link-l);
  --bulma-dropdown-item-selected-background-l: var(--bulma-link-l);
  --bulma-dropdown-item-selected-color-l: var(--bulma-link-invert-l);
  --bulma-dropdown-divider-background-color: var(--bulma-border-weak);
}

.bulma-dropdown {
  display: inline-flex;
  position: relative;
  vertical-align: top;
}
.bulma-dropdown.bulma-is-active .bulma-dropdown-menu, .bulma-dropdown.bulma-is-hoverable:hover .bulma-dropdown-menu {
  display: block;
}
.bulma-dropdown.bulma-is-right .bulma-dropdown-menu {
  left: auto;
  right: 0;
}
.bulma-dropdown.bulma-is-up .bulma-dropdown-menu {
  bottom: 100%;
  padding-bottom: var(--bulma-dropdown-content-offset);
  padding-top: initial;
  top: auto;
}

.bulma-dropdown-menu {
  display: none;
  left: 0;
  min-width: var(--bulma-dropdown-menu-min-width);
  padding-top: var(--bulma-dropdown-content-offset);
  position: absolute;
  top: 100%;
  z-index: var(--bulma-dropdown-content-z);
}

.bulma-dropdown-content {
  background-color: var(--bulma-dropdown-content-background-color);
  border-radius: var(--bulma-dropdown-content-radius);
  box-shadow: var(--bulma-dropdown-content-shadow);
  padding-bottom: var(--bulma-dropdown-content-padding-bottom);
  padding-top: var(--bulma-dropdown-content-padding-top);
}

.bulma-dropdown-item {
  color: hsl(var(--bulma-dropdown-item-h), var(--bulma-dropdown-item-s), var(--bulma-dropdown-item-color-l));
  display: block;
  font-size: 0.875rem;
  line-height: 1.5;
  padding: 0.375rem 1rem;
}

a.bulma-dropdown-item,
button.bulma-dropdown-item {
  background-color: hsl(var(--bulma-dropdown-item-h), var(--bulma-dropdown-item-s), calc(var(--bulma-dropdown-item-background-l) + var(--bulma-dropdown-item-background-l-delta)));
  padding-inline-end: 3rem;
  text-align: inherit;
  white-space: nowrap;
  width: 100%;
}
a.bulma-dropdown-item:hover,
button.bulma-dropdown-item:hover {
  --bulma-dropdown-item-background-l-delta: var(--bulma-dropdown-item-hover-background-l-delta);
  --bulma-dropdown-item-border-l-delta: var(--bulma-dropdown-item-hover-border-l-delta);
}
a.bulma-dropdown-item:active,
button.bulma-dropdown-item:active {
  --bulma-dropdown-item-background-l-delta: var(--bulma-dropdown-item-active-background-l-delta);
  --bulma-dropdown-item-border-l-delta: var(--bulma-dropdown-item-active-border-l-delta);
}
a.bulma-dropdown-item.bulma-is-active, a.bulma-dropdown-item.bulma-is-selected,
button.bulma-dropdown-item.bulma-is-active,
button.bulma-dropdown-item.bulma-is-selected {
  --bulma-dropdown-item-h: var(--bulma-dropdown-item-selected-h);
  --bulma-dropdown-item-s: var(--bulma-dropdown-item-selected-s);
  --bulma-dropdown-item-l: var(--bulma-dropdown-item-selected-l);
  --bulma-dropdown-item-background-l: var(--bulma-dropdown-item-selected-background-l);
  --bulma-dropdown-item-color-l: var(--bulma-dropdown-item-selected-color-l);
}

.bulma-dropdown-divider {
  background-color: var(--bulma-dropdown-divider-background-color);
  border: none;
  display: block;
  height: 1px;
  margin: 0.5rem 0;
}

.bulma-menu {
  --bulma-menu-item-h: var(--bulma-scheme-h);
  --bulma-menu-item-s: var(--bulma-scheme-s);
  --bulma-menu-item-l: var(--bulma-scheme-main-l);
  --bulma-menu-item-background-l: var(--bulma-scheme-main-l);
  --bulma-menu-item-background-l-delta: 0%;
  --bulma-menu-item-hover-background-l-delta: var(--bulma-hover-background-l-delta);
  --bulma-menu-item-active-background-l-delta: var(--bulma-active-background-l-delta);
  --bulma-menu-item-color-l: var(--bulma-text-l);
  --bulma-menu-item-radius: var(--bulma-radius-small);
  --bulma-menu-item-selected-h: var(--bulma-link-h);
  --bulma-menu-item-selected-s: var(--bulma-link-s);
  --bulma-menu-item-selected-l: var(--bulma-link-l);
  --bulma-menu-item-selected-background-l: var(--bulma-link-l);
  --bulma-menu-item-selected-color-l: var(--bulma-link-invert-l);
  --bulma-menu-list-border-left: 1px solid var(--bulma-border);
  --bulma-menu-list-line-height: 1.25;
  --bulma-menu-list-link-padding: 0.5em 0.75em;
  --bulma-menu-nested-list-margin: 0.75em;
  --bulma-menu-nested-list-padding-left: 0.75em;
  --bulma-menu-label-color: var(--bulma-text-weak);
  --bulma-menu-label-font-size: 0.75em;
  --bulma-menu-label-letter-spacing: 0.1em;
  --bulma-menu-label-spacing: 1em;
}

.bulma-menu {
  font-size: var(--bulma-size-normal);
}
.bulma-menu.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-menu.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-menu.bulma-is-large {
  font-size: var(--bulma-size-large);
}

.bulma-menu-list {
  line-height: var(--bulma-menu-list-line-height);
}
.bulma-menu-list a,
.bulma-menu-list button,
.bulma-menu-list .bulma-menu-item {
  background-color: hsl(var(--bulma-menu-item-h), var(--bulma-menu-item-s), calc(var(--bulma-menu-item-background-l) + var(--bulma-menu-item-background-l-delta)));
  border-radius: var(--bulma-menu-item-radius);
  color: hsl(var(--bulma-menu-item-h), var(--bulma-menu-item-s), var(--bulma-menu-item-color-l));
  display: block;
  padding: var(--bulma-menu-list-link-padding);
  text-align: left;
  width: 100%;
}
.bulma-menu-list a:hover,
.bulma-menu-list button:hover,
.bulma-menu-list .bulma-menu-item:hover {
  --bulma-menu-item-background-l-delta: var(--bulma-menu-item-hover-background-l-delta);
}
.bulma-menu-list a:active,
.bulma-menu-list button:active,
.bulma-menu-list .bulma-menu-item:active {
  --bulma-menu-item-background-l-delta: var(--bulma-menu-item-active-background-l-delta);
}
.bulma-menu-list a.bulma-is-active, .bulma-menu-list a.bulma-is-selected,
.bulma-menu-list button.bulma-is-active,
.bulma-menu-list button.bulma-is-selected,
.bulma-menu-list .bulma-menu-item.bulma-is-active,
.bulma-menu-list .bulma-menu-item.bulma-is-selected {
  --bulma-menu-item-h: var(--bulma-menu-item-selected-h);
  --bulma-menu-item-s: var(--bulma-menu-item-selected-s);
  --bulma-menu-item-l: var(--bulma-menu-item-selected-l);
  --bulma-menu-item-background-l: var(--bulma-menu-item-selected-background-l);
  --bulma-menu-item-color-l: var(--bulma-menu-item-selected-color-l);
}
.bulma-menu-list li ul {
  border-inline-start: var(--bulma-menu-list-border-left);
  margin: var(--bulma-menu-nested-list-margin);
  padding-inline-start: var(--bulma-menu-nested-list-padding-left);
}

.bulma-menu-label {
  color: var(--bulma-menu-label-color);
  font-size: var(--bulma-menu-label-font-size);
  letter-spacing: var(--bulma-menu-label-letter-spacing);
  text-transform: uppercase;
}
.bulma-menu-label:not(:first-child) {
  margin-top: var(--bulma-menu-label-spacing);
}
.bulma-menu-label:not(:last-child) {
  margin-bottom: var(--bulma-menu-label-spacing);
}

.bulma-message {
  --bulma-message-border-l-delta: -20%;
  --bulma-message-radius: var(--bulma-radius);
  --bulma-message-header-weight: var(--bulma-weight-semibold);
  --bulma-message-header-padding: 1em 1.25em;
  --bulma-message-header-radius: var(--bulma-radius);
  --bulma-message-body-border-width: 0 0 0 4px;
  --bulma-message-body-color: var(--bulma-text);
  --bulma-message-body-padding: 1.25em 1.5em;
  --bulma-message-body-radius: var(--bulma-radius-small);
  --bulma-message-body-pre-code-background-color: transparent;
  --bulma-message-header-body-border-width: 0;
  --bulma-message-h: var(--bulma-scheme-h);
  --bulma-message-s: var(--bulma-scheme-s);
  --bulma-message-background-l: var(--bulma-background-l);
  --bulma-message-border-l: var(--bulma-border-l);
  --bulma-message-border-style: solid;
  --bulma-message-border-width: 0.25em;
  --bulma-message-color-l: var(--bulma-text-l);
  --bulma-message-header-background-l: var(--bulma-dark-l);
  --bulma-message-header-color-l: var(--bulma-text-dark-invert-l);
}

.bulma-message {
  border-radius: var(--bulma-message-radius);
  color: hsl(var(--bulma-message-h), var(--bulma-message-s), var(--bulma-message-color-l));
  font-size: var(--bulma-size-normal);
}
.bulma-message strong {
  color: currentColor;
}
.bulma-message a:not(.bulma-button):not(.bulma-tag):not(.bulma-dropdown-item) {
  color: currentColor;
  text-decoration: underline;
}
.bulma-message.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-message.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-message.bulma-is-large {
  font-size: var(--bulma-size-large);
}
.bulma-message.bulma-is-white {
  --bulma-message-h: var(--bulma-white-h);
  --bulma-message-s: var(--bulma-white-s);
  --bulma-message-border-l: calc(var(--bulma-white-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-white-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-white-l);
  --bulma-message-header-color-l: var(--bulma-white-invert-l);
}
.bulma-message.bulma-is-black {
  --bulma-message-h: var(--bulma-black-h);
  --bulma-message-s: var(--bulma-black-s);
  --bulma-message-border-l: calc(var(--bulma-black-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-black-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-black-l);
  --bulma-message-header-color-l: var(--bulma-black-invert-l);
}
.bulma-message.bulma-is-light {
  --bulma-message-h: var(--bulma-light-h);
  --bulma-message-s: var(--bulma-light-s);
  --bulma-message-border-l: calc(var(--bulma-light-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-light-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-light-l);
  --bulma-message-header-color-l: var(--bulma-light-invert-l);
}
.bulma-message.bulma-is-dark {
  --bulma-message-h: var(--bulma-dark-h);
  --bulma-message-s: var(--bulma-dark-s);
  --bulma-message-border-l: calc(var(--bulma-dark-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-dark-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-dark-l);
  --bulma-message-header-color-l: var(--bulma-dark-invert-l);
}
.bulma-message.bulma-is-text {
  --bulma-message-h: var(--bulma-text-h);
  --bulma-message-s: var(--bulma-text-s);
  --bulma-message-border-l: calc(var(--bulma-text-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-text-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-text-l);
  --bulma-message-header-color-l: var(--bulma-text-invert-l);
}
.bulma-message.bulma-is-primary {
  --bulma-message-h: var(--bulma-primary-h);
  --bulma-message-s: var(--bulma-primary-s);
  --bulma-message-border-l: calc(var(--bulma-primary-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-primary-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-primary-l);
  --bulma-message-header-color-l: var(--bulma-primary-invert-l);
}
.bulma-message.bulma-is-link {
  --bulma-message-h: var(--bulma-link-h);
  --bulma-message-s: var(--bulma-link-s);
  --bulma-message-border-l: calc(var(--bulma-link-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-link-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-link-l);
  --bulma-message-header-color-l: var(--bulma-link-invert-l);
}
.bulma-message.bulma-is-info {
  --bulma-message-h: var(--bulma-info-h);
  --bulma-message-s: var(--bulma-info-s);
  --bulma-message-border-l: calc(var(--bulma-info-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-info-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-info-l);
  --bulma-message-header-color-l: var(--bulma-info-invert-l);
}
.bulma-message.bulma-is-success {
  --bulma-message-h: var(--bulma-success-h);
  --bulma-message-s: var(--bulma-success-s);
  --bulma-message-border-l: calc(var(--bulma-success-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-success-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-success-l);
  --bulma-message-header-color-l: var(--bulma-success-invert-l);
}
.bulma-message.bulma-is-warning {
  --bulma-message-h: var(--bulma-warning-h);
  --bulma-message-s: var(--bulma-warning-s);
  --bulma-message-border-l: calc(var(--bulma-warning-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-warning-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-warning-l);
  --bulma-message-header-color-l: var(--bulma-warning-invert-l);
}
.bulma-message.bulma-is-danger {
  --bulma-message-h: var(--bulma-danger-h);
  --bulma-message-s: var(--bulma-danger-s);
  --bulma-message-border-l: calc(var(--bulma-danger-l) + var(--bulma-message-border-l-delta));
  --bulma-message-color-l: var(--bulma-danger-on-scheme-l);
  --bulma-message-header-background-l: var(--bulma-danger-l);
  --bulma-message-header-color-l: var(--bulma-danger-invert-l);
}

.bulma-message-header {
  align-items: center;
  background-color: hsl(var(--bulma-message-h), var(--bulma-message-s), var(--bulma-message-header-background-l));
  border-start-start-radius: var(--bulma-message-header-radius);
  border-start-end-radius: var(--bulma-message-header-radius);
  color: hsl(var(--bulma-message-h), var(--bulma-message-s), var(--bulma-message-header-color-l));
  display: flex;
  font-weight: var(--bulma-message-header-weight);
  justify-content: space-between;
  line-height: 1.25;
  padding: var(--bulma-message-header-padding);
  position: relative;
}
.bulma-message-header .bulma-delete {
  flex-grow: 0;
  flex-shrink: 0;
  margin-inline-start: 0.75em;
}
.bulma-message-header + .bulma-message-body {
  border-width: var(--bulma-message-header-body-border-width);
  border-start-start-radius: 0;
  border-start-end-radius: 0;
}

.bulma-message-body {
  background-color: hsl(var(--bulma-message-h), var(--bulma-message-s), var(--bulma-message-background-l));
  border-inline-start-color: hsl(var(--bulma-message-h), var(--bulma-message-s), var(--bulma-message-border-l));
  border-inline-start-style: var(--bulma-message-border-style);
  border-inline-start-width: var(--bulma-message-border-width);
  border-radius: var(--bulma-message-body-radius);
  padding: var(--bulma-message-body-padding);
}
.bulma-message-body code,
.bulma-message-body pre {
  background-color: hsl(var(--bulma-message-h), var(--bulma-message-s), var(--bulma-message-header-color-l));
  color: hsl(var(--bulma-message-h), var(--bulma-message-s), var(--bulma-message-header-background-l));
}
.bulma-message-body pre code {
  background-color: var(--bulma-message-body-pre-code-background-color);
}

.bulma-modal {
  --bulma-modal-z: 40;
  --bulma-modal-background-background-color: hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.86);
  --bulma-modal-content-width: 40rem;
  --bulma-modal-content-margin-mobile: 1.25rem;
  --bulma-modal-content-spacing-mobile: 10rem;
  --bulma-modal-content-spacing-tablet: 2.5rem;
  --bulma-modal-close-dimensions: 2.5rem;
  --bulma-modal-close-right: 1.25rem;
  --bulma-modal-close-top: 1.25rem;
  --bulma-modal-card-spacing: 2.5rem;
  --bulma-modal-card-head-background-color: var(--bulma-scheme-main);
  --bulma-modal-card-head-padding: 2rem;
  --bulma-modal-card-head-radius: var(--bulma-radius-large);
  --bulma-modal-card-title-color: var(--bulma-text-strong);
  --bulma-modal-card-title-line-height: 1;
  --bulma-modal-card-title-size: var(--bulma-size-4);
  --bulma-modal-card-foot-background-color: var(--bulma-scheme-main-bis);
  --bulma-modal-card-foot-radius: var(--bulma-radius-large);
  --bulma-modal-card-body-background-color: var(--bulma-scheme-main);
  --bulma-modal-card-body-padding: 2rem;
}

.bulma-modal {
  align-items: center;
  display: none;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  position: fixed;
  z-index: var(--bulma-modal-z);
}
.bulma-modal.bulma-is-active {
  display: flex;
}

.bulma-modal-background {
  background-color: var(--bulma-modal-background-background-color);
}

.bulma-modal-content,
.bulma-modal-card {
  margin: 0 var(--bulma-modal-content-margin-mobile);
  max-height: calc(100vh - var(--bulma-modal-content-spacing-mobile));
  overflow: auto;
  position: relative;
  width: 100%;
}
@media screen and (min-width: 769px) {
  .bulma-modal-content,
  .bulma-modal-card {
    margin: 0 auto;
    max-height: calc(100vh - var(--bulma-modal-content-spacing-tablet));
    width: var(--bulma-modal-content-width);
  }
}

.bulma-modal-close {
  background: none;
  height: var(--bulma-modal-close-dimensions);
  inset-inline-end: var(--bulma-modal-close-right);
  position: fixed;
  top: var(--bulma-modal-close-top);
  width: var(--bulma-modal-close-dimensions);
}

.bulma-modal-card {
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - var(--bulma-modal-card-spacing));
  overflow: hidden;
  overflow-y: visible;
}

.bulma-modal-card-head,
.bulma-modal-card-foot {
  align-items: center;
  display: flex;
  flex-shrink: 0;
  justify-content: flex-start;
  padding: var(--bulma-modal-card-head-padding);
  position: relative;
}

.bulma-modal-card-head {
  background-color: var(--bulma-modal-card-head-background-color);
  border-start-start-radius: var(--bulma-modal-card-head-radius);
  border-start-end-radius: var(--bulma-modal-card-head-radius);
  box-shadow: var(--bulma-shadow);
}

.bulma-modal-card-title {
  color: var(--bulma-modal-card-title-color);
  flex-grow: 1;
  flex-shrink: 0;
  font-size: var(--bulma-modal-card-title-size);
  line-height: var(--bulma-modal-card-title-line-height);
}

.bulma-modal-card-foot {
  background-color: var(--bulma-modal-card-foot-background-color);
  border-end-start-radius: var(--bulma-modal-card-foot-radius);
  border-end-end-radius: var(--bulma-modal-card-foot-radius);
}

.bulma-modal-card-body {
  -webkit-overflow-scrolling: touch;
  background-color: var(--bulma-modal-card-body-background-color);
  flex-grow: 1;
  flex-shrink: 1;
  overflow: auto;
  padding: var(--bulma-modal-card-body-padding);
}

:root {
  --bulma-navbar-height: 3.25rem;
}

.bulma-navbar {
  --bulma-navbar-h: var(--bulma-scheme-h);
  --bulma-navbar-s: var(--bulma-scheme-s);
  --bulma-navbar-l: var(--bulma-scheme-main-l);
  --bulma-navbar-background-color: var(--bulma-scheme-main);
  --bulma-navbar-box-shadow-size: 0 0.125em 0 0;
  --bulma-navbar-box-shadow-color: var(--bulma-background);
  --bulma-navbar-padding-vertical: 1rem;
  --bulma-navbar-padding-horizontal: 2rem;
  --bulma-navbar-z: 30;
  --bulma-navbar-fixed-z: 30;
  --bulma-navbar-item-background-a: 0;
  --bulma-navbar-item-background-l: var(--bulma-scheme-main-l);
  --bulma-navbar-item-background-l-delta: 0%;
  --bulma-navbar-item-hover-background-l-delta: var(--bulma-hover-background-l-delta);
  --bulma-navbar-item-active-background-l-delta: var(--bulma-active-background-l-delta);
  --bulma-navbar-item-color-l: var(--bulma-text-l);
  --bulma-navbar-item-color: hsl(var(--bulma-navbar-h), var(--bulma-navbar-s), var(--bulma-navbar-item-color-l));
  --bulma-navbar-item-selected-h: var(--bulma-link-h);
  --bulma-navbar-item-selected-s: var(--bulma-link-s);
  --bulma-navbar-item-selected-l: var(--bulma-link-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-link-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-link-invert-l);
  --bulma-navbar-item-img-max-height: 1.75rem;
  --bulma-navbar-burger-color: var(--bulma-link);
  --bulma-navbar-tab-hover-background-color: transparent;
  --bulma-navbar-tab-hover-border-bottom-color: var(--bulma-link);
  --bulma-navbar-tab-active-color: var(--bulma-link);
  --bulma-navbar-tab-active-background-color: transparent;
  --bulma-navbar-tab-active-border-bottom-color: var(--bulma-link);
  --bulma-navbar-tab-active-border-bottom-style: solid;
  --bulma-navbar-tab-active-border-bottom-width: 0.1875em;
  --bulma-navbar-dropdown-background-color: var(--bulma-scheme-main);
  --bulma-navbar-dropdown-border-l: var(--bulma-border-l);
  --bulma-navbar-dropdown-border-color: hsl(var(--bulma-navbar-h), var(--bulma-navbar-s), var(--bulma-navbar-dropdown-border-l));
  --bulma-navbar-dropdown-border-style: solid;
  --bulma-navbar-dropdown-border-width: 0.125em;
  --bulma-navbar-dropdown-offset: -0.25em;
  --bulma-navbar-dropdown-arrow: var(--bulma-link);
  --bulma-navbar-dropdown-radius: var(--bulma-radius-large);
  --bulma-navbar-dropdown-z: 20;
  --bulma-navbar-dropdown-boxed-radius: var(--bulma-radius-large);
  --bulma-navbar-dropdown-boxed-shadow: 0 0.5em 0.5em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1), 0 0 0 1px hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1);
  --bulma-navbar-dropdown-item-h: var(--bulma-scheme-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-scheme-s);
  --bulma-navbar-dropdown-item-l: var(--bulma-scheme-main-l);
  --bulma-navbar-dropdown-item-background-l: var(--bulma-scheme-main-l);
  --bulma-navbar-dropdown-item-color-l: var(--bulma-text-l);
  --bulma-navbar-divider-background-l: var(--bulma-background-l);
  --bulma-navbar-divider-height: 0.125em;
  --bulma-navbar-bottom-box-shadow-size: 0 -0.125em 0 0;
}

.bulma-navbar {
  background-color: var(--bulma-navbar-background-color);
  min-height: var(--bulma-navbar-height);
  position: relative;
  z-index: var(--bulma-navbar-z);
}
.bulma-navbar.bulma-is-white {
  --bulma-navbar-h: var(--bulma-white-h);
  --bulma-navbar-s: var(--bulma-white-s);
  --bulma-navbar-l: var(--bulma-white-l);
  --bulma-burger-h: var(--bulma-white-h);
  --bulma-burger-s: var(--bulma-white-s);
  --bulma-burger-l: var(--bulma-white-invert-l);
  --bulma-navbar-background-color: var(--bulma-white);
  --bulma-navbar-item-background-l: var(--bulma-white-l);
  --bulma-navbar-item-color-l: var(--bulma-white-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-white-h);
  --bulma-navbar-item-selected-s: var(--bulma-white-s);
  --bulma-navbar-item-selected-l: var(--bulma-white-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-white-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-white-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-white-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-white-h), var(--bulma-white-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-white-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-white-s);
}
.bulma-navbar.bulma-is-black {
  --bulma-navbar-h: var(--bulma-black-h);
  --bulma-navbar-s: var(--bulma-black-s);
  --bulma-navbar-l: var(--bulma-black-l);
  --bulma-burger-h: var(--bulma-black-h);
  --bulma-burger-s: var(--bulma-black-s);
  --bulma-burger-l: var(--bulma-black-invert-l);
  --bulma-navbar-background-color: var(--bulma-black);
  --bulma-navbar-item-background-l: var(--bulma-black-l);
  --bulma-navbar-item-color-l: var(--bulma-black-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-black-h);
  --bulma-navbar-item-selected-s: var(--bulma-black-s);
  --bulma-navbar-item-selected-l: var(--bulma-black-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-black-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-black-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-black-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-black-h), var(--bulma-black-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-black-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-black-s);
}
.bulma-navbar.bulma-is-light {
  --bulma-navbar-h: var(--bulma-light-h);
  --bulma-navbar-s: var(--bulma-light-s);
  --bulma-navbar-l: var(--bulma-light-l);
  --bulma-burger-h: var(--bulma-light-h);
  --bulma-burger-s: var(--bulma-light-s);
  --bulma-burger-l: var(--bulma-light-invert-l);
  --bulma-navbar-background-color: var(--bulma-light);
  --bulma-navbar-item-background-l: var(--bulma-light-l);
  --bulma-navbar-item-color-l: var(--bulma-light-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-light-h);
  --bulma-navbar-item-selected-s: var(--bulma-light-s);
  --bulma-navbar-item-selected-l: var(--bulma-light-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-light-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-light-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-light-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-light-h), var(--bulma-light-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-light-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-light-s);
}
.bulma-navbar.bulma-is-dark {
  --bulma-navbar-h: var(--bulma-dark-h);
  --bulma-navbar-s: var(--bulma-dark-s);
  --bulma-navbar-l: var(--bulma-dark-l);
  --bulma-burger-h: var(--bulma-dark-h);
  --bulma-burger-s: var(--bulma-dark-s);
  --bulma-burger-l: var(--bulma-dark-invert-l);
  --bulma-navbar-background-color: var(--bulma-dark);
  --bulma-navbar-item-background-l: var(--bulma-dark-l);
  --bulma-navbar-item-color-l: var(--bulma-dark-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-dark-h);
  --bulma-navbar-item-selected-s: var(--bulma-dark-s);
  --bulma-navbar-item-selected-l: var(--bulma-dark-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-dark-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-dark-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-dark-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-dark-h), var(--bulma-dark-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-dark-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-dark-s);
}
.bulma-navbar.bulma-is-text {
  --bulma-navbar-h: var(--bulma-text-h);
  --bulma-navbar-s: var(--bulma-text-s);
  --bulma-navbar-l: var(--bulma-text-l);
  --bulma-burger-h: var(--bulma-text-h);
  --bulma-burger-s: var(--bulma-text-s);
  --bulma-burger-l: var(--bulma-text-invert-l);
  --bulma-navbar-background-color: var(--bulma-text);
  --bulma-navbar-item-background-l: var(--bulma-text-l);
  --bulma-navbar-item-color-l: var(--bulma-text-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-text-h);
  --bulma-navbar-item-selected-s: var(--bulma-text-s);
  --bulma-navbar-item-selected-l: var(--bulma-text-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-text-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-text-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-text-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-text-h), var(--bulma-text-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-text-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-text-s);
}
.bulma-navbar.bulma-is-primary {
  --bulma-navbar-h: var(--bulma-primary-h);
  --bulma-navbar-s: var(--bulma-primary-s);
  --bulma-navbar-l: var(--bulma-primary-l);
  --bulma-burger-h: var(--bulma-primary-h);
  --bulma-burger-s: var(--bulma-primary-s);
  --bulma-burger-l: var(--bulma-primary-invert-l);
  --bulma-navbar-background-color: var(--bulma-primary);
  --bulma-navbar-item-background-l: var(--bulma-primary-l);
  --bulma-navbar-item-color-l: var(--bulma-primary-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-primary-h);
  --bulma-navbar-item-selected-s: var(--bulma-primary-s);
  --bulma-navbar-item-selected-l: var(--bulma-primary-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-primary-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-primary-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-primary-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-primary-h), var(--bulma-primary-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-primary-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-primary-s);
}
.bulma-navbar.bulma-is-link {
  --bulma-navbar-h: var(--bulma-link-h);
  --bulma-navbar-s: var(--bulma-link-s);
  --bulma-navbar-l: var(--bulma-link-l);
  --bulma-burger-h: var(--bulma-link-h);
  --bulma-burger-s: var(--bulma-link-s);
  --bulma-burger-l: var(--bulma-link-invert-l);
  --bulma-navbar-background-color: var(--bulma-link);
  --bulma-navbar-item-background-l: var(--bulma-link-l);
  --bulma-navbar-item-color-l: var(--bulma-link-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-link-h);
  --bulma-navbar-item-selected-s: var(--bulma-link-s);
  --bulma-navbar-item-selected-l: var(--bulma-link-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-link-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-link-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-link-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-link-h), var(--bulma-link-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-link-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-link-s);
}
.bulma-navbar.bulma-is-info {
  --bulma-navbar-h: var(--bulma-info-h);
  --bulma-navbar-s: var(--bulma-info-s);
  --bulma-navbar-l: var(--bulma-info-l);
  --bulma-burger-h: var(--bulma-info-h);
  --bulma-burger-s: var(--bulma-info-s);
  --bulma-burger-l: var(--bulma-info-invert-l);
  --bulma-navbar-background-color: var(--bulma-info);
  --bulma-navbar-item-background-l: var(--bulma-info-l);
  --bulma-navbar-item-color-l: var(--bulma-info-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-info-h);
  --bulma-navbar-item-selected-s: var(--bulma-info-s);
  --bulma-navbar-item-selected-l: var(--bulma-info-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-info-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-info-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-info-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-info-h), var(--bulma-info-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-info-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-info-s);
}
.bulma-navbar.bulma-is-success {
  --bulma-navbar-h: var(--bulma-success-h);
  --bulma-navbar-s: var(--bulma-success-s);
  --bulma-navbar-l: var(--bulma-success-l);
  --bulma-burger-h: var(--bulma-success-h);
  --bulma-burger-s: var(--bulma-success-s);
  --bulma-burger-l: var(--bulma-success-invert-l);
  --bulma-navbar-background-color: var(--bulma-success);
  --bulma-navbar-item-background-l: var(--bulma-success-l);
  --bulma-navbar-item-color-l: var(--bulma-success-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-success-h);
  --bulma-navbar-item-selected-s: var(--bulma-success-s);
  --bulma-navbar-item-selected-l: var(--bulma-success-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-success-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-success-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-success-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-success-h), var(--bulma-success-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-success-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-success-s);
}
.bulma-navbar.bulma-is-warning {
  --bulma-navbar-h: var(--bulma-warning-h);
  --bulma-navbar-s: var(--bulma-warning-s);
  --bulma-navbar-l: var(--bulma-warning-l);
  --bulma-burger-h: var(--bulma-warning-h);
  --bulma-burger-s: var(--bulma-warning-s);
  --bulma-burger-l: var(--bulma-warning-invert-l);
  --bulma-navbar-background-color: var(--bulma-warning);
  --bulma-navbar-item-background-l: var(--bulma-warning-l);
  --bulma-navbar-item-color-l: var(--bulma-warning-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-warning-h);
  --bulma-navbar-item-selected-s: var(--bulma-warning-s);
  --bulma-navbar-item-selected-l: var(--bulma-warning-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-warning-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-warning-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-warning-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-warning-h), var(--bulma-warning-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-warning-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-warning-s);
}
.bulma-navbar.bulma-is-danger {
  --bulma-navbar-h: var(--bulma-danger-h);
  --bulma-navbar-s: var(--bulma-danger-s);
  --bulma-navbar-l: var(--bulma-danger-l);
  --bulma-burger-h: var(--bulma-danger-h);
  --bulma-burger-s: var(--bulma-danger-s);
  --bulma-burger-l: var(--bulma-danger-invert-l);
  --bulma-navbar-background-color: var(--bulma-danger);
  --bulma-navbar-item-background-l: var(--bulma-danger-l);
  --bulma-navbar-item-color-l: var(--bulma-danger-invert-l);
  --bulma-navbar-item-selected-h: var(--bulma-danger-h);
  --bulma-navbar-item-selected-s: var(--bulma-danger-s);
  --bulma-navbar-item-selected-l: var(--bulma-danger-l);
  --bulma-navbar-item-selected-background-l: var(--bulma-danger-l);
  --bulma-navbar-item-selected-color-l: var(--bulma-danger-invert-l);
  --bulma-navbar-dropdown-arrow: var(--bulma-danger-invert-l);
  --bulma-navbar-dropdown-background-color: hsl(var(--bulma-danger-h), var(--bulma-danger-s), var(--bulma-navbar-dropdown-item-background-l));
  --bulma-navbar-dropdown-item-h: var(--bulma-danger-h);
  --bulma-navbar-dropdown-item-s: var(--bulma-danger-s);
}
.bulma-navbar > .bulma-container {
  align-items: stretch;
  display: flex;
  min-height: var(--bulma-navbar-height);
  width: 100%;
}
.bulma-navbar.bulma-has-shadow {
  box-shadow: var(--bulma-navbar-box-shadow-size) var(--bulma-navbar-box-shadow-color);
}
.bulma-navbar.bulma-is-fixed-bottom, .bulma-navbar.bulma-is-fixed-top {
  left: 0;
  position: fixed;
  right: 0;
  z-index: var(--bulma-navbar-fixed-z);
}
.bulma-navbar.bulma-is-fixed-bottom {
  bottom: 0;
}
.bulma-navbar.bulma-is-fixed-bottom.bulma-has-shadow {
  box-shadow: var(--bulma-navbar-bottom-box-shadow-size) var(--bulma-navbar-box-shadow-color);
}
.bulma-navbar.bulma-is-fixed-top {
  top: 0;
}

html.bulma-has-navbar-fixed-top,
body.bulma-has-navbar-fixed-top {
  padding-top: var(--bulma-navbar-height);
}
html.bulma-has-navbar-fixed-bottom,
body.bulma-has-navbar-fixed-bottom {
  padding-bottom: var(--bulma-navbar-height);
}

.bulma-navbar-brand,
.bulma-navbar-tabs {
  align-items: stretch;
  display: flex;
  flex-shrink: 0;
  min-height: var(--bulma-navbar-height);
}

.bulma-navbar-tabs {
  -webkit-overflow-scrolling: touch;
  max-width: 100vw;
  overflow-x: auto;
  overflow-y: hidden;
}

.bulma-navbar-burger {
  align-items: center;
  appearance: none;
  background: none;
  border: none;
  border-radius: var(--bulma-burger-border-radius);
  color: hsl(var(--bulma-burger-h), var(--bulma-burger-s), var(--bulma-burger-l));
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  flex-shrink: 0;
  height: 2.5rem;
  justify-content: center;
  position: relative;
  vertical-align: top;
  width: 2.5rem;
}
.bulma-navbar-burger span {
  background-color: currentColor;
  display: block;
  height: var(--bulma-burger-item-height);
  left: calc(50% - (var(--bulma-burger-item-width)) / 2);
  position: absolute;
  transform-origin: center;
  transition-duration: var(--bulma-duration);
  transition-property: background-color, color, opacity, transform;
  transition-timing-function: var(--bulma-easing);
  width: var(--bulma-burger-item-width);
}
.bulma-navbar-burger span:nth-child(1), .bulma-navbar-burger span:nth-child(2) {
  top: calc(50% - (var(--bulma-burger-item-height)) / 2);
}
.bulma-navbar-burger span:nth-child(3) {
  bottom: calc(50% + var(--bulma-burger-gap));
}
.bulma-navbar-burger span:nth-child(4) {
  top: calc(50% + var(--bulma-burger-gap));
}
.bulma-navbar-burger:hover {
  background-color: hsla(var(--bulma-burger-h), var(--bulma-burger-s), var(--bulma-burger-l), 0.1);
}
.bulma-navbar-burger:active {
  background-color: hsla(var(--bulma-burger-h), var(--bulma-burger-s), var(--bulma-burger-l), 0.2);
}
.bulma-navbar-burger.bulma-is-active span:nth-child(1) {
  transform: rotate(-45deg);
}
.bulma-navbar-burger.bulma-is-active span:nth-child(2) {
  transform: rotate(45deg);
}
.bulma-navbar-burger.bulma-is-active span:nth-child(3), .bulma-navbar-burger.bulma-is-active span:nth-child(4) {
  opacity: 0;
}
.bulma-navbar-burger {
  align-self: center;
  color: var(--bulma-navbar-burger-color);
  margin-inline-start: auto;
  margin-inline-end: 0.375rem;
}

.bulma-navbar-menu {
  display: none;
}

.bulma-navbar-item,
.bulma-navbar-link {
  color: var(--bulma-navbar-item-color);
  display: block;
  gap: 0.75rem;
  line-height: 1.5;
  padding: 0.5rem 0.75rem;
  position: relative;
}
.bulma-navbar-item .bulma-icon:only-child,
.bulma-navbar-link .bulma-icon:only-child {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

a.bulma-navbar-item,
.bulma-navbar-link {
  background-color: hsla(var(--bulma-navbar-h), var(--bulma-navbar-s), calc(var(--bulma-navbar-item-background-l) + var(--bulma-navbar-item-background-l-delta)), var(--bulma-navbar-item-background-a));
  cursor: pointer;
}
a.bulma-navbar-item:focus, a.bulma-navbar-item:focus-within, a.bulma-navbar-item:hover,
.bulma-navbar-link:focus,
.bulma-navbar-link:focus-within,
.bulma-navbar-link:hover {
  --bulma-navbar-item-background-l-delta: var(--bulma-navbar-item-hover-background-l-delta);
  --bulma-navbar-item-background-a: 1;
}
a.bulma-navbar-item:active,
.bulma-navbar-link:active {
  --bulma-navbar-item-background-l-delta: var(--bulma-navbar-item-active-background-l-delta);
  --bulma-navbar-item-background-a: 1;
}
a.bulma-navbar-item.bulma-is-active, a.bulma-navbar-item.bulma-is-selected,
.bulma-navbar-link.bulma-is-active,
.bulma-navbar-link.bulma-is-selected {
  --bulma-navbar-h: var(--bulma-navbar-item-selected-h);
  --bulma-navbar-s: var(--bulma-navbar-item-selected-s);
  --bulma-navbar-l: var(--bulma-navbar-item-selected-l);
  --bulma-navbar-item-background-l: var(--bulma-navbar-item-selected-background-l);
  --bulma-navbar-item-background-a: 1;
  --bulma-navbar-item-color-l: var(--bulma-navbar-item-selected-color-l);
}

.bulma-navbar-item {
  flex-grow: 0;
  flex-shrink: 0;
}
.bulma-navbar-item img,
.bulma-navbar-item svg {
  max-height: var(--bulma-navbar-item-img-max-height);
}
.bulma-navbar-item.bulma-has-dropdown {
  padding: 0;
}
.bulma-navbar-item.bulma-is-expanded {
  flex-grow: 1;
  flex-shrink: 1;
}
.bulma-navbar-item.bulma-is-tab {
  border-bottom: 1px solid transparent;
  min-height: var(--bulma-navbar-height);
  padding-bottom: calc(0.5rem - 1px);
}
.bulma-navbar-item.bulma-is-tab:focus, .bulma-navbar-item.bulma-is-tab:hover {
  background-color: var(--bulma-navbar-tab-hover-background-color);
  border-bottom-color: var(--bulma-navbar-tab-hover-border-bottom-color);
}
.bulma-navbar-item.bulma-is-tab.bulma-is-active {
  background-color: var(--bulma-navbar-tab-active-background-color);
  border-bottom-color: var(--bulma-navbar-tab-active-border-bottom-color);
  border-bottom-style: var(--bulma-navbar-tab-active-border-bottom-style);
  border-bottom-width: var(--bulma-navbar-tab-active-border-bottom-width);
  color: var(--bulma-navbar-tab-active-color);
  padding-bottom: calc(0.5rem - var(--bulma-navbar-tab-active-border-bottom-width));
}

.bulma-navbar-content {
  flex-grow: 1;
  flex-shrink: 1;
}

.bulma-navbar-link:not(.bulma-is-arrowless) {
  padding-inline-end: 2.5em;
}
.bulma-navbar-link:not(.bulma-is-arrowless)::after {
  border-color: var(--bulma-navbar-dropdown-arrow);
  margin-top: -0.375em;
  inset-inline-end: 1.125em;
}

.bulma-navbar-dropdown {
  font-size: 0.875rem;
  padding-bottom: 0.75rem;
  padding-top: 0.5rem;
}
.bulma-navbar-dropdown .bulma-navbar-item {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.bulma-navbar-dropdown .bulma-navbar-item:not(.is-active, .is-selected) {
  background-color: hsl(var(--bulma-navbar-dropdown-item-h), var(--bulma-navbar-dropdown-item-s), calc(var(--bulma-navbar-dropdown-item-background-l) + var(--bulma-navbar-item-background-l-delta)));
  color: hsl(var(--bulma-navbar-dropdown-item-h), var(--bulma-navbar-dropdown-item-s), var(--bulma-navbar-dropdown-item-color-l));
}

.bulma-navbar-divider {
  background-color: hsl(var(--bulma-navbar-h), var(--bulma-navbar-s), var(--bulma-navbar-divider-background-l));
  border: none;
  display: none;
  height: var(--bulma-navbar-divider-height);
  margin: 0.5rem 0;
}

@media screen and (max-width: 1023px) {
  .bulma-navbar > .bulma-container {
    display: block;
  }
  .bulma-navbar-brand .bulma-navbar-item,
  .bulma-navbar-tabs .bulma-navbar-item {
    align-items: center;
    display: flex;
  }
  .bulma-navbar-link::after {
    display: none;
  }
  .bulma-navbar-menu {
    background-color: var(--bulma-navbar-background-color);
    box-shadow: 0 0.5em 1em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1);
    padding: 0.5rem 0;
  }
  .bulma-navbar-menu.bulma-is-active {
    display: block;
  }
  .bulma-navbar.bulma-is-fixed-bottom-touch, .bulma-navbar.bulma-is-fixed-top-touch {
    left: 0;
    position: fixed;
    right: 0;
    z-index: var(--bulma-navbar-fixed-z);
  }
  .bulma-navbar.bulma-is-fixed-bottom-touch {
    bottom: 0;
  }
  .bulma-navbar.bulma-is-fixed-bottom-touch.bulma-has-shadow {
    box-shadow: 0 -0.125em 0.1875em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1);
  }
  .bulma-navbar.bulma-is-fixed-top-touch {
    top: 0;
  }
  .bulma-navbar.bulma-is-fixed-top .bulma-navbar-menu, .bulma-navbar.bulma-is-fixed-top-touch .bulma-navbar-menu {
    -webkit-overflow-scrolling: touch;
    max-height: calc(100vh - var(--bulma-navbar-height));
    overflow: auto;
  }
  html.bulma-has-navbar-fixed-top-touch,
  body.bulma-has-navbar-fixed-top-touch {
    padding-top: var(--bulma-navbar-height);
  }
  html.bulma-has-navbar-fixed-bottom-touch,
  body.bulma-has-navbar-fixed-bottom-touch {
    padding-bottom: var(--bulma-navbar-height);
  }
}
@media screen and (min-width: 1024px) {
  .bulma-navbar,
  .bulma-navbar-menu,
  .bulma-navbar-start,
  .bulma-navbar-end {
    align-items: stretch;
    display: flex;
  }
  .bulma-navbar {
    min-height: var(--bulma-navbar-height);
  }
  .bulma-navbar.bulma-is-spaced {
    padding: var(--bulma-navbar-padding-vertical) var(--bulma-navbar-padding-horizontal);
  }
  .bulma-navbar.bulma-is-spaced .bulma-navbar-start,
  .bulma-navbar.bulma-is-spaced .bulma-navbar-end {
    align-items: center;
  }
  .bulma-navbar.bulma-is-spaced a.bulma-navbar-item,
  .bulma-navbar.bulma-is-spaced .bulma-navbar-link {
    border-radius: var(--bulma-radius);
  }
  .bulma-navbar.bulma-is-transparent {
    --bulma-navbar-item-background-a: 0;
  }
  .bulma-navbar.bulma-is-transparent .bulma-navbar-dropdown a.bulma-navbar-item {
    background-color: hsl(var(--bulma-navbar-h), var(--bulma-navbar-s), calc(var(--bulma-navbar-item-background-l) + var(--bulma-navbar-item-background-l-delta)));
  }
  .bulma-navbar.bulma-is-transparent .bulma-navbar-dropdown a.bulma-navbar-item.bulma-is-active, .bulma-navbar.bulma-is-transparent .bulma-navbar-dropdown a.bulma-navbar-item.bulma-is-selected {
    --bulma-navbar-h: var(--bulma-navbar-item-selected-h);
    --bulma-navbar-s: var(--bulma-navbar-item-selected-s);
    --bulma-navbar-l: var(--bulma-navbar-item-selected-l);
    --bulma-navbar-item-background-l: var(--bulma-navbar-item-selected-background-l);
    --bulma-navbar-item-color-l: var(--bulma-navbar-item-selected-color-l);
  }
  .bulma-navbar-burger {
    display: none;
  }
  .bulma-navbar-item,
  .bulma-navbar-link {
    align-items: center;
    display: flex;
  }
  .bulma-navbar-item.bulma-has-dropdown {
    align-items: stretch;
  }
  .bulma-navbar-item.bulma-has-dropdown-up .bulma-navbar-link::after {
    transform: rotate(135deg) translate(0.25em, -0.25em);
  }
  .bulma-navbar-item.bulma-has-dropdown-up .bulma-navbar-dropdown {
    border-bottom-color: var(--bulma-navbar-dropdown-border-color);
    border-bottom-style: var(--bulma-navbar-dropdown-border-style);
    border-bottom-width: var(--bulma-navbar-dropdown-border-width);
    border-radius: var(--bulma-navbar-dropdown-radius) var(--bulma-navbar-dropdown-radius) 0 0;
    border-top: none;
    bottom: 100%;
    box-shadow: 0 -0.5em 0.5em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1);
    top: auto;
  }
  .bulma-navbar-item.bulma-is-active .bulma-navbar-dropdown, .bulma-navbar-item.bulma-is-hoverable:focus .bulma-navbar-dropdown, .bulma-navbar-item.bulma-is-hoverable:focus-within .bulma-navbar-dropdown, .bulma-navbar-item.bulma-is-hoverable:hover .bulma-navbar-dropdown {
    display: block;
  }
  .bulma-navbar.bulma-is-spaced .bulma-navbar-item.bulma-is-active .bulma-navbar-dropdown, .bulma-navbar-item.bulma-is-active .bulma-navbar-dropdown.bulma-is-boxed, .bulma-navbar.bulma-is-spaced .bulma-navbar-item.bulma-is-hoverable:focus .bulma-navbar-dropdown, .bulma-navbar-item.bulma-is-hoverable:focus .bulma-navbar-dropdown.bulma-is-boxed, .bulma-navbar.bulma-is-spaced .bulma-navbar-item.bulma-is-hoverable:focus-within .bulma-navbar-dropdown, .bulma-navbar-item.bulma-is-hoverable:focus-within .bulma-navbar-dropdown.bulma-is-boxed, .bulma-navbar.bulma-is-spaced .bulma-navbar-item.bulma-is-hoverable:hover .bulma-navbar-dropdown, .bulma-navbar-item.bulma-is-hoverable:hover .bulma-navbar-dropdown.bulma-is-boxed {
    opacity: 1;
    pointer-events: auto;
    transform: translateY(0);
  }
  .bulma-navbar-menu {
    flex-grow: 1;
    flex-shrink: 0;
  }
  .bulma-navbar-start {
    justify-content: flex-start;
    margin-inline-end: auto;
  }
  .bulma-navbar-end {
    justify-content: flex-end;
    margin-inline-start: auto;
  }
  .bulma-navbar-dropdown {
    background-color: var(--bulma-navbar-dropdown-background-color);
    border-end-start-radius: var(--bulma-navbar-dropdown-radius);
    border-end-end-radius: var(--bulma-navbar-dropdown-radius);
    border-top-color: var(--bulma-navbar-dropdown-border-color);
    border-top-style: var(--bulma-navbar-dropdown-border-style);
    border-top-width: var(--bulma-navbar-dropdown-border-width);
    box-shadow: 0 0.5em 0.5em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1);
    display: none;
    font-size: 0.875rem;
    inset-inline-start: 0;
    min-width: 100%;
    position: absolute;
    top: 100%;
    z-index: var(--bulma-navbar-dropdown-z);
  }
  .bulma-navbar-dropdown .bulma-navbar-item {
    padding: 0.375rem 1rem;
    white-space: nowrap;
  }
  .bulma-navbar-dropdown a.bulma-navbar-item {
    padding-inline-end: 3rem;
  }
  .bulma-navbar-dropdown a.bulma-navbar-item:not(.is-active, .is-selected) {
    background-color: hsl(var(--bulma-navbar-dropdown-item-h), var(--bulma-navbar-dropdown-item-s), calc(var(--bulma-navbar-dropdown-item-background-l) + var(--bulma-navbar-item-background-l-delta)));
    color: hsl(var(--bulma-navbar-dropdown-item-h), var(--bulma-navbar-dropdown-item-s), var(--bulma-navbar-dropdown-item-color-l));
  }
  .bulma-navbar.bulma-is-spaced .bulma-navbar-dropdown, .bulma-navbar-dropdown.bulma-is-boxed {
    border-radius: var(--bulma-navbar-dropdown-boxed-radius);
    border-top: none;
    box-shadow: var(--bulma-navbar-dropdown-boxed-shadow);
    display: block;
    opacity: 0;
    pointer-events: none;
    top: calc(100% + (var(--bulma-navbar-dropdown-offset)));
    transform: translateY(-5px);
    transition-duration: var(--bulma-duration);
    transition-property: opacity, transform;
  }
  .bulma-navbar-dropdown.bulma-is-right {
    left: auto;
    right: 0;
  }
  .bulma-navbar-divider {
    display: block;
  }
  .bulma-navbar > .bulma-container .bulma-navbar-brand,
  .bulma-container > .bulma-navbar .bulma-navbar-brand {
    margin-inline-start: -0.75rem;
  }
  .bulma-navbar > .bulma-container .bulma-navbar-menu,
  .bulma-container > .bulma-navbar .bulma-navbar-menu {
    margin-inline-end: -0.75rem;
  }
  .bulma-navbar.bulma-is-fixed-bottom-desktop, .bulma-navbar.bulma-is-fixed-top-desktop {
    left: 0;
    position: fixed;
    right: 0;
    z-index: var(--bulma-navbar-fixed-z);
  }
  .bulma-navbar.bulma-is-fixed-bottom-desktop {
    bottom: 0;
  }
  .bulma-navbar.bulma-is-fixed-bottom-desktop.bulma-has-shadow {
    box-shadow: 0 -0.125em 0.1875em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.1);
  }
  .bulma-navbar.bulma-is-fixed-top-desktop {
    top: 0;
  }
  html.bulma-has-navbar-fixed-top-desktop,
  body.bulma-has-navbar-fixed-top-desktop {
    padding-top: var(--bulma-navbar-height);
  }
  html.bulma-has-navbar-fixed-bottom-desktop,
  body.bulma-has-navbar-fixed-bottom-desktop {
    padding-bottom: var(--bulma-navbar-height);
  }
  html.bulma-has-spaced-navbar-fixed-top,
  body.bulma-has-spaced-navbar-fixed-top {
    padding-top: calc(var(--bulma-navbar-height) + var(--bulma-navbar-padding-vertical) * 2);
  }
  html.bulma-has-spaced-navbar-fixed-bottom,
  body.bulma-has-spaced-navbar-fixed-bottom {
    padding-bottom: calc(var(--bulma-navbar-height) + var(--bulma-navbar-padding-vertical) * 2);
  }
}
.bulma-hero.bulma-is-fullheight-with-navbar {
  min-height: calc(100vh - var(--bulma-navbar-height));
}

.bulma-pagination {
  --bulma-pagination-margin: -0.25rem;
  --bulma-pagination-min-width: var(--bulma-control-height);
  --bulma-pagination-item-h: var(--bulma-scheme-h);
  --bulma-pagination-item-s: var(--bulma-scheme-s);
  --bulma-pagination-item-l: var(--bulma-scheme-main-l);
  --bulma-pagination-item-background-l-delta: 0%;
  --bulma-pagination-item-hover-background-l-delta: var(--bulma-hover-background-l-delta);
  --bulma-pagination-item-active-background-l-delta: var(--bulma-active-background-l-delta);
  --bulma-pagination-item-border-style: solid;
  --bulma-pagination-item-border-width: var(--bulma-control-border-width);
  --bulma-pagination-item-border-l: var(--bulma-border-l);
  --bulma-pagination-item-border-l-delta: 0%;
  --bulma-pagination-item-hover-border-l-delta: var(--bulma-hover-border-l-delta);
  --bulma-pagination-item-active-border-l-delta: var(--bulma-active-border-l-delta);
  --bulma-pagination-item-focus-border-l-delta: var(--bulma-focus-border-l-delta);
  --bulma-pagination-item-color-l: var(--bulma-text-strong-l);
  --bulma-pagination-item-font-size: 1em;
  --bulma-pagination-item-margin: 0.25rem;
  --bulma-pagination-item-padding-left: 0.5em;
  --bulma-pagination-item-padding-right: 0.5em;
  --bulma-pagination-item-outer-shadow-h: 0;
  --bulma-pagination-item-outer-shadow-s: 0%;
  --bulma-pagination-item-outer-shadow-l: 20%;
  --bulma-pagination-item-outer-shadow-a: 0.05;
  --bulma-pagination-nav-padding-left: 0.75em;
  --bulma-pagination-nav-padding-right: 0.75em;
  --bulma-pagination-disabled-color: var(--bulma-text-weak);
  --bulma-pagination-disabled-background-color: var(--bulma-border);
  --bulma-pagination-disabled-border-color: var(--bulma-border);
  --bulma-pagination-current-color: var(--bulma-link-invert);
  --bulma-pagination-current-background-color: var(--bulma-link);
  --bulma-pagination-current-border-color: var(--bulma-link);
  --bulma-pagination-ellipsis-color: var(--bulma-text-weak);
  --bulma-pagination-shadow-inset: inset 0 0.0625em 0.125em hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-scheme-invert-l), 0.2);
  --bulma-pagination-selected-item-h: var(--bulma-link-h);
  --bulma-pagination-selected-item-s: var(--bulma-link-s);
  --bulma-pagination-selected-item-l: var(--bulma-link-l);
  --bulma-pagination-selected-item-background-l: var(--bulma-link-l);
  --bulma-pagination-selected-item-border-l: var(--bulma-link-l);
  --bulma-pagination-selected-item-color-l: var(--bulma-link-invert-l);
}

.bulma-pagination {
  font-size: var(--bulma-size-normal);
  margin: var(--bulma-pagination-margin);
}
.bulma-pagination.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-pagination.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-pagination.bulma-is-large {
  font-size: var(--bulma-size-large);
}
.bulma-pagination.bulma-is-rounded .bulma-pagination-previous,
.bulma-pagination.bulma-is-rounded .bulma-pagination-next {
  padding-left: 1em;
  padding-right: 1em;
  border-radius: var(--bulma-radius-rounded);
}
.bulma-pagination.bulma-is-rounded .bulma-pagination-link {
  border-radius: var(--bulma-radius-rounded);
}

.bulma-pagination,
.bulma-pagination-list {
  align-items: center;
  display: flex;
  justify-content: center;
  text-align: center;
}

.bulma-pagination-previous,
.bulma-pagination-next,
.bulma-pagination-link,
.bulma-pagination-ellipsis {
  color: hsl(var(--bulma-pagination-item-h), var(--bulma-pagination-item-s), var(--bulma-pagination-item-color-l));
  font-size: var(--bulma-pagination-item-font-size);
  justify-content: center;
  margin: var(--bulma-pagination-item-margin);
  padding-left: var(--bulma-pagination-item-padding-left);
  padding-right: var(--bulma-pagination-item-padding-right);
  text-align: center;
}

.bulma-pagination-previous,
.bulma-pagination-next,
.bulma-pagination-link {
  background-color: hsl(var(--bulma-pagination-item-h), var(--bulma-pagination-item-s), calc(var(--bulma-pagination-item-background-l) + var(--bulma-pagination-item-background-l-delta)));
  border-color: hsl(var(--bulma-pagination-item-h), var(--bulma-pagination-item-s), calc(var(--bulma-pagination-item-border-l) + var(--bulma-pagination-item-border-l-delta)));
  border-style: var(--bulma-pagination-item-border-style);
  border-width: var(--bulma-pagination-item-border-width);
  box-shadow: 0px 0.0625em 0.125em hsla(var(--bulma-pagination-item-outer-shadow-h), var(--bulma-pagination-item-outer-shadow-s), var(--bulma-pagination-item-outer-shadow-l), var(--bulma-pagination-item-outer-shadow-a)), 0px 0.125em 0.25em hsla(var(--bulma-pagination-item-outer-shadow-h), var(--bulma-pagination-item-outer-shadow-s), var(--bulma-pagination-item-outer-shadow-l), var(--bulma-pagination-item-outer-shadow-a));
  color: hsl(var(--bulma-pagination-item-h), var(--bulma-pagination-item-s), var(--bulma-pagination-item-color-l));
  min-width: var(--bulma-pagination-min-width);
  transition-duration: var(--bulma-duration);
  transition-property: background-color, border-color, box-shadow, color;
}
.bulma-pagination-previous:hover,
.bulma-pagination-next:hover,
.bulma-pagination-link:hover {
  --bulma-pagination-item-background-l-delta: var(--bulma-pagination-item-hover-background-l-delta);
  --bulma-pagination-item-border-l-delta: var(--bulma-pagination-item-hover-border-l-delta);
}
.bulma-pagination-previous:focus,
.bulma-pagination-next:focus,
.bulma-pagination-link:focus {
  --bulma-pagination-item-background-l-delta: var(--bulma-pagination-item-hover-background-l-delta);
  --bulma-pagination-item-border-l-delta: var(--bulma-pagination-item-hover-border-l-delta);
}
.bulma-pagination-previous:active,
.bulma-pagination-next:active,
.bulma-pagination-link:active {
  box-shadow: var(--bulma-pagination-shadow-inset);
}
.bulma-pagination-previous[disabled], .bulma-pagination-previous.bulma-is-disabled,
.bulma-pagination-next[disabled],
.bulma-pagination-next.bulma-is-disabled,
.bulma-pagination-link[disabled],
.bulma-pagination-link.bulma-is-disabled {
  background-color: var(--bulma-pagination-disabled-background-color);
  border-color: var(--bulma-pagination-disabled-border-color);
  box-shadow: none;
  color: var(--bulma-pagination-disabled-color);
  opacity: 0.5;
}

.bulma-pagination-previous,
.bulma-pagination-next {
  padding-left: var(--bulma-pagination-nav-padding-left);
  padding-right: var(--bulma-pagination-nav-padding-right);
  white-space: nowrap;
}

.bulma-pagination-link.bulma-is-current, .bulma-pagination-link.bulma-is-selected {
  --bulma-pagination-item-h: var(--bulma-pagination-selected-item-h);
  --bulma-pagination-item-s: var(--bulma-pagination-selected-item-s);
  --bulma-pagination-item-l: var(--bulma-pagination-selected-item-l);
  --bulma-pagination-item-background-l: var(--bulma-pagination-selected-item-background-l);
  --bulma-pagination-item-border-l: var(--bulma-pagination-selected-item-border-l);
  --bulma-pagination-item-color-l: var(--bulma-pagination-selected-item-color-l);
}

.bulma-pagination-ellipsis {
  color: var(--bulma-pagination-ellipsis-color);
  pointer-events: none;
}

.bulma-pagination-list {
  flex-wrap: wrap;
}
.bulma-pagination-list li {
  list-style: none;
}

@media screen and (max-width: 768px) {
  .bulma-pagination {
    flex-wrap: wrap;
  }
  .bulma-pagination-previous,
  .bulma-pagination-next {
    flex-grow: 1;
    flex-shrink: 1;
  }
  .bulma-pagination-list li {
    flex-grow: 1;
    flex-shrink: 1;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-pagination-list {
    flex-grow: 1;
    flex-shrink: 1;
    justify-content: flex-start;
    order: 1;
  }
  .bulma-pagination-previous,
  .bulma-pagination-next,
  .bulma-pagination-link,
  .bulma-pagination-ellipsis {
    margin-bottom: 0;
    margin-top: 0;
  }
  .bulma-pagination-previous {
    order: 2;
  }
  .bulma-pagination-next {
    order: 3;
  }
  .bulma-pagination {
    justify-content: space-between;
    margin-bottom: 0;
    margin-top: 0;
  }
  .bulma-pagination.bulma-is-centered .bulma-pagination-previous {
    order: 1;
  }
  .bulma-pagination.bulma-is-centered .bulma-pagination-list {
    justify-content: center;
    order: 2;
  }
  .bulma-pagination.bulma-is-centered .bulma-pagination-next {
    order: 3;
  }
  .bulma-pagination.bulma-is-right .bulma-pagination-previous {
    order: 1;
  }
  .bulma-pagination.bulma-is-right .bulma-pagination-next {
    order: 2;
  }
  .bulma-pagination.bulma-is-right .bulma-pagination-list {
    justify-content: flex-end;
    order: 3;
  }
}
.bulma-panel {
  --bulma-panel-margin: var(--bulma-block-spacing);
  --bulma-panel-item-border: 1px solid var(--bulma-border-weak);
  --bulma-panel-radius: var(--bulma-radius-large);
  --bulma-panel-shadow: var(--bulma-shadow);
  --bulma-panel-heading-line-height: 1.25;
  --bulma-panel-heading-padding: 1em 1.25em;
  --bulma-panel-heading-radius: var(--bulma-radius);
  --bulma-panel-heading-size: 1.25em;
  --bulma-panel-heading-weight: var(--bulma-weight-bold);
  --bulma-panel-tabs-font-size: 1em;
  --bulma-panel-tab-border-bottom-color: var(--bulma-border);
  --bulma-panel-tab-border-bottom-style: solid;
  --bulma-panel-tab-border-bottom-width: 1px;
  --bulma-panel-tab-active-color: var(--bulma-link-active);
  --bulma-panel-list-item-color: var(--bulma-text);
  --bulma-panel-list-item-hover-color: var(--bulma-link);
  --bulma-panel-block-color: var(--bulma-text-strong);
  --bulma-panel-block-hover-background-color: var(--bulma-background);
  --bulma-panel-block-active-border-left-color: var(--bulma-link);
  --bulma-panel-block-active-color: var(--bulma-link-active);
  --bulma-panel-block-active-icon-color: var(--bulma-link);
  --bulma-panel-icon-color: var(--bulma-text-weak);
}

.bulma-panel {
  --bulma-panel-h: var(--bulma-scheme-h);
  --bulma-panel-s: var(--bulma-scheme-s);
  --bulma-panel-color-l: var(--bulma-text-l);
  --bulma-panel-heading-background-l: var(--bulma-text-l);
  --bulma-panel-heading-color-l: var(--bulma-text-invert-l);
  border-radius: var(--bulma-panel-radius);
  box-shadow: var(--bulma-panel-shadow);
  font-size: var(--bulma-size-normal);
}
.bulma-panel:not(:last-child) {
  margin-bottom: var(--bulma-panel-margin);
}
.bulma-panel.bulma-is-white {
  --bulma-panel-h: var(--bulma-white-h);
  --bulma-panel-s: var(--bulma-white-s);
  --bulma-panel-color-l: var(--bulma-white-l);
  --bulma-panel-heading-background-l: var(--bulma-white-l);
  --bulma-panel-heading-color-l: var(--bulma-white-invert-l);
}
.bulma-panel.bulma-is-black {
  --bulma-panel-h: var(--bulma-black-h);
  --bulma-panel-s: var(--bulma-black-s);
  --bulma-panel-color-l: var(--bulma-black-l);
  --bulma-panel-heading-background-l: var(--bulma-black-l);
  --bulma-panel-heading-color-l: var(--bulma-black-invert-l);
}
.bulma-panel.bulma-is-light {
  --bulma-panel-h: var(--bulma-light-h);
  --bulma-panel-s: var(--bulma-light-s);
  --bulma-panel-color-l: var(--bulma-light-l);
  --bulma-panel-heading-background-l: var(--bulma-light-l);
  --bulma-panel-heading-color-l: var(--bulma-light-invert-l);
}
.bulma-panel.bulma-is-dark {
  --bulma-panel-h: var(--bulma-dark-h);
  --bulma-panel-s: var(--bulma-dark-s);
  --bulma-panel-color-l: var(--bulma-dark-l);
  --bulma-panel-heading-background-l: var(--bulma-dark-l);
  --bulma-panel-heading-color-l: var(--bulma-dark-invert-l);
}
.bulma-panel.bulma-is-text {
  --bulma-panel-h: var(--bulma-text-h);
  --bulma-panel-s: var(--bulma-text-s);
  --bulma-panel-color-l: var(--bulma-text-l);
  --bulma-panel-heading-background-l: var(--bulma-text-l);
  --bulma-panel-heading-color-l: var(--bulma-text-invert-l);
}
.bulma-panel.bulma-is-primary {
  --bulma-panel-h: var(--bulma-primary-h);
  --bulma-panel-s: var(--bulma-primary-s);
  --bulma-panel-color-l: var(--bulma-primary-l);
  --bulma-panel-heading-background-l: var(--bulma-primary-l);
  --bulma-panel-heading-color-l: var(--bulma-primary-invert-l);
}
.bulma-panel.bulma-is-link {
  --bulma-panel-h: var(--bulma-link-h);
  --bulma-panel-s: var(--bulma-link-s);
  --bulma-panel-color-l: var(--bulma-link-l);
  --bulma-panel-heading-background-l: var(--bulma-link-l);
  --bulma-panel-heading-color-l: var(--bulma-link-invert-l);
}
.bulma-panel.bulma-is-info {
  --bulma-panel-h: var(--bulma-info-h);
  --bulma-panel-s: var(--bulma-info-s);
  --bulma-panel-color-l: var(--bulma-info-l);
  --bulma-panel-heading-background-l: var(--bulma-info-l);
  --bulma-panel-heading-color-l: var(--bulma-info-invert-l);
}
.bulma-panel.bulma-is-success {
  --bulma-panel-h: var(--bulma-success-h);
  --bulma-panel-s: var(--bulma-success-s);
  --bulma-panel-color-l: var(--bulma-success-l);
  --bulma-panel-heading-background-l: var(--bulma-success-l);
  --bulma-panel-heading-color-l: var(--bulma-success-invert-l);
}
.bulma-panel.bulma-is-warning {
  --bulma-panel-h: var(--bulma-warning-h);
  --bulma-panel-s: var(--bulma-warning-s);
  --bulma-panel-color-l: var(--bulma-warning-l);
  --bulma-panel-heading-background-l: var(--bulma-warning-l);
  --bulma-panel-heading-color-l: var(--bulma-warning-invert-l);
}
.bulma-panel.bulma-is-danger {
  --bulma-panel-h: var(--bulma-danger-h);
  --bulma-panel-s: var(--bulma-danger-s);
  --bulma-panel-color-l: var(--bulma-danger-l);
  --bulma-panel-heading-background-l: var(--bulma-danger-l);
  --bulma-panel-heading-color-l: var(--bulma-danger-invert-l);
}

.bulma-panel-tabs:not(:last-child),
.bulma-panel-block:not(:last-child) {
  border-bottom: var(--bulma-panel-item-border);
}

.bulma-panel-heading {
  background-color: hsl(var(--bulma-panel-h), var(--bulma-panel-s), var(--bulma-panel-heading-background-l));
  border-radius: var(--bulma-panel-radius) var(--bulma-panel-radius) 0 0;
  color: hsl(var(--bulma-panel-h), var(--bulma-panel-s), var(--bulma-panel-heading-color-l));
  font-size: var(--bulma-panel-heading-size);
  font-weight: var(--bulma-panel-heading-weight);
  line-height: var(--bulma-panel-heading-line-height);
  padding: var(--bulma-panel-heading-padding);
}

.bulma-panel-tabs {
  align-items: flex-end;
  display: flex;
  font-size: var(--bulma-panel-tabs-font-size);
  justify-content: center;
}
.bulma-panel-tabs a {
  border-bottom-color: var(--bulma-panel-tab-border-bottom-color);
  border-bottom-style: var(--bulma-panel-tab-border-bottom-style);
  border-bottom-width: var(--bulma-panel-tab-border-bottom-width);
  margin-bottom: calc(-1 * 1px);
  padding: 0.75em;
}
.bulma-panel-tabs a.bulma-is-active {
  border-bottom-color: hsl(var(--bulma-panel-h), var(--bulma-panel-s), var(--bulma-panel-color-l));
  color: var(--bulma-panel-tab-active-color);
}

.bulma-panel-list a {
  color: var(--bulma-panel-list-item-color);
}
.bulma-panel-list a:hover {
  color: var(--bulma-panel-list-item-hover-color);
}

.bulma-panel-block {
  align-items: center;
  color: var(--bulma-panel-block-color);
  display: flex;
  justify-content: flex-start;
  padding: 0.75em 1em;
}
.bulma-panel-block input[type=checkbox] {
  margin-inline-end: 0.75em;
}
.bulma-panel-block > .bulma-control {
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
}
.bulma-panel-block.bulma-is-wrapped {
  flex-wrap: wrap;
}
.bulma-panel-block.bulma-is-active {
  border-left-color: var(--bulma-panel-block-active-border-left-color);
  color: var(--bulma-panel-block-active-color);
}
.bulma-panel-block.bulma-is-active .bulma-panel-icon {
  color: hsl(var(--bulma-panel-h), var(--bulma-panel-s), var(--bulma-panel-color-l));
}
.bulma-panel-block:last-child {
  border-end-start-radius: var(--bulma-panel-radius);
  border-end-end-radius: var(--bulma-panel-radius);
}

a.bulma-panel-block,
label.bulma-panel-block {
  cursor: pointer;
}
a.bulma-panel-block:hover,
label.bulma-panel-block:hover {
  background-color: var(--bulma-panel-block-hover-background-color);
}

.bulma-panel-icon {
  display: inline-block;
  font-size: 1em;
  height: 1em;
  line-height: 1em;
  text-align: center;
  vertical-align: top;
  width: 1em;
  color: var(--bulma-panel-icon-color);
  margin-inline-end: 0.75em;
}
.bulma-panel-icon .bulma-fa {
  font-size: inherit;
  line-height: inherit;
}

.bulma-tabs {
  --bulma-tabs-border-bottom-color: var(--bulma-border);
  --bulma-tabs-border-bottom-style: solid;
  --bulma-tabs-border-bottom-width: 1px;
  --bulma-tabs-link-color: var(--bulma-text);
  --bulma-tabs-link-hover-border-bottom-color: var(--bulma-text-strong);
  --bulma-tabs-link-hover-color: var(--bulma-text-strong);
  --bulma-tabs-link-active-border-bottom-color: var(--bulma-link-text);
  --bulma-tabs-link-active-color: var(--bulma-link-text);
  --bulma-tabs-link-padding: 0.5em 1em;
  --bulma-tabs-boxed-link-radius: var(--bulma-radius);
  --bulma-tabs-boxed-link-hover-background-color: var(--bulma-background);
  --bulma-tabs-boxed-link-hover-border-bottom-color: var(--bulma-border);
  --bulma-tabs-boxed-link-active-background-color: var(--bulma-scheme-main);
  --bulma-tabs-boxed-link-active-border-color: var(--bulma-border);
  --bulma-tabs-boxed-link-active-border-bottom-color: transparent;
  --bulma-tabs-toggle-link-border-color: var(--bulma-border);
  --bulma-tabs-toggle-link-border-style: solid;
  --bulma-tabs-toggle-link-border-width: 1px;
  --bulma-tabs-toggle-link-hover-background-color: var(--bulma-background);
  --bulma-tabs-toggle-link-hover-border-color: var(--bulma-border-hover);
  --bulma-tabs-toggle-link-radius: var(--bulma-radius);
  --bulma-tabs-toggle-link-active-background-color: var(--bulma-link);
  --bulma-tabs-toggle-link-active-border-color: var(--bulma-link);
  --bulma-tabs-toggle-link-active-color: var(--bulma-link-invert);
}

.bulma-tabs {
  -webkit-overflow-scrolling: touch;
  align-items: stretch;
  display: flex;
  font-size: var(--bulma-size-normal);
  justify-content: space-between;
  overflow: hidden;
  overflow-x: auto;
  white-space: nowrap;
}
.bulma-tabs a {
  align-items: center;
  border-bottom-color: var(--bulma-tabs-border-bottom-color);
  border-bottom-style: var(--bulma-tabs-border-bottom-style);
  border-bottom-width: var(--bulma-tabs-border-bottom-width);
  color: var(--bulma-tabs-link-color);
  display: flex;
  justify-content: center;
  margin-bottom: calc(-1 * var(--bulma-tabs-border-bottom-width));
  padding: var(--bulma-tabs-link-padding);
  transition-duration: var(--bulma-duration);
  transition-property: background-color, border-color, color;
  vertical-align: top;
}
.bulma-tabs a:hover {
  border-bottom-color: var(--bulma-tabs-link-hover-border-bottom-color);
  color: var(--bulma-tabs-link-hover-color);
}
.bulma-tabs li {
  display: block;
}
.bulma-tabs li.bulma-is-active a {
  border-bottom-color: var(--bulma-tabs-link-active-border-bottom-color);
  color: var(--bulma-tabs-link-active-color);
}
.bulma-tabs ul {
  align-items: center;
  border-bottom-color: var(--bulma-tabs-border-bottom-color);
  border-bottom-style: var(--bulma-tabs-border-bottom-style);
  border-bottom-width: var(--bulma-tabs-border-bottom-width);
  display: flex;
  flex-grow: 1;
  flex-shrink: 0;
  justify-content: flex-start;
}
.bulma-tabs ul.bulma-is-left {
  padding-right: 0.75em;
}
.bulma-tabs ul.bulma-is-center {
  flex: none;
  justify-content: center;
  padding-left: 0.75em;
  padding-right: 0.75em;
}
.bulma-tabs ul.bulma-is-right {
  justify-content: flex-end;
  padding-left: 0.75em;
}
.bulma-tabs .bulma-icon:first-child {
  margin-inline-end: 0.5em;
}
.bulma-tabs .bulma-icon:last-child {
  margin-inline-start: 0.5em;
}
.bulma-tabs.bulma-is-centered ul {
  justify-content: center;
}
.bulma-tabs.bulma-is-right ul {
  justify-content: flex-end;
}
.bulma-tabs.bulma-is-boxed a {
  border: 1px solid transparent;
  border-start-start-radius: var(--bulma-tabs-boxed-link-radius);
  border-start-end-radius: var(--bulma-tabs-boxed-link-radius);
}
.bulma-tabs.bulma-is-boxed a:hover {
  background-color: var(--bulma-tabs-boxed-link-hover-background-color);
  border-bottom-color: var(--bulma-tabs-boxed-link-hover-border-bottom-color);
}
.bulma-tabs.bulma-is-boxed li.bulma-is-active a {
  background-color: var(--bulma-tabs-boxed-link-active-background-color);
  border-color: var(--bulma-tabs-boxed-link-active-border-color);
  border-bottom-color: var(--bulma-tabs-boxed-link-active-border-bottom-color) !important;
}
.bulma-tabs.bulma-is-fullwidth li {
  flex-grow: 1;
  flex-shrink: 0;
}
.bulma-tabs.bulma-is-toggle a {
  border-color: var(--bulma-tabs-toggle-link-border-color);
  border-style: var(--bulma-tabs-toggle-link-border-style);
  border-width: var(--bulma-tabs-toggle-link-border-width);
  margin-bottom: 0;
  position: relative;
}
.bulma-tabs.bulma-is-toggle a:hover {
  background-color: var(--bulma-tabs-toggle-link-hover-background-color);
  border-color: var(--bulma-tabs-toggle-link-hover-border-color);
  z-index: 2;
}
.bulma-tabs.bulma-is-toggle li + li {
  margin-inline-start: calc(-1 * var(--bulma-tabs-toggle-link-border-width));
}
.bulma-tabs.bulma-is-toggle li:first-child a {
  border-start-start-radius: var(--bulma-tabs-toggle-link-radius);
  border-end-start-radius: var(--bulma-tabs-toggle-link-radius);
}
.bulma-tabs.bulma-is-toggle li:last-child a {
  border-start-end-radius: var(--bulma-tabs-toggle-link-radius);
  border-end-end-radius: var(--bulma-tabs-toggle-link-radius);
}
.bulma-tabs.bulma-is-toggle li.bulma-is-active a {
  background-color: var(--bulma-tabs-toggle-link-active-background-color);
  border-color: var(--bulma-tabs-toggle-link-active-border-color);
  color: var(--bulma-tabs-toggle-link-active-color);
  z-index: 1;
}
.bulma-tabs.bulma-is-toggle ul {
  border-bottom: none;
}
.bulma-tabs.bulma-is-toggle.bulma-is-toggle-rounded li:first-child a {
  border-start-start-radius: var(--bulma-radius-rounded);
  border-end-start-radius: var(--bulma-radius-rounded);
  padding-inline-start: 1.25em;
}
.bulma-tabs.bulma-is-toggle.bulma-is-toggle-rounded li:last-child a {
  border-start-end-radius: var(--bulma-radius-rounded);
  border-end-end-radius: var(--bulma-radius-rounded);
  padding-inline-end: 1.25em;
}
.bulma-tabs.bulma-is-small {
  font-size: var(--bulma-size-small);
}
.bulma-tabs.bulma-is-medium {
  font-size: var(--bulma-size-medium);
}
.bulma-tabs.bulma-is-large {
  font-size: var(--bulma-size-large);
}

/* Bulma Grid */
:root {
  --bulma-column-gap: 0.75rem;
}

.bulma-column {
  display: block;
  flex-basis: 0;
  flex-grow: 1;
  flex-shrink: 1;
  padding: var(--bulma-column-gap);
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-narrow {
  flex: none;
  width: unset;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-full {
  flex: none;
  width: 100%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-three-quarters {
  flex: none;
  width: 75%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-two-thirds {
  flex: none;
  width: 66.6666%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-half {
  flex: none;
  width: 50%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-one-third {
  flex: none;
  width: 33.3333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-one-quarter {
  flex: none;
  width: 25%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-one-fifth {
  flex: none;
  width: 20%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-two-fifths {
  flex: none;
  width: 40%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-three-fifths {
  flex: none;
  width: 60%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-four-fifths {
  flex: none;
  width: 80%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-three-quarters {
  margin-inline-start: 75%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-two-thirds {
  margin-inline-start: 66.6666%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-half {
  margin-inline-start: 50%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-one-third {
  margin-inline-start: 0.3333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-one-quarter {
  margin-inline-start: 25%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-one-fifth {
  margin-inline-start: 20%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-two-fifths {
  margin-inline-start: 40%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-three-fifths {
  margin-inline-start: 60%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-four-fifths {
  margin-inline-start: 80%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-0 {
  flex: none;
  width: 0%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-0 {
  margin-inline-start: 0%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-1 {
  flex: none;
  width: 8.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-1 {
  margin-inline-start: 8.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-2 {
  flex: none;
  width: 16.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-2 {
  margin-inline-start: 16.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-3 {
  flex: none;
  width: 25%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-3 {
  margin-inline-start: 25%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-4 {
  flex: none;
  width: 33.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-4 {
  margin-inline-start: 33.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-5 {
  flex: none;
  width: 41.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-5 {
  margin-inline-start: 41.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-6 {
  flex: none;
  width: 50%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-6 {
  margin-inline-start: 50%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-7 {
  flex: none;
  width: 58.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-7 {
  margin-inline-start: 58.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-8 {
  flex: none;
  width: 66.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-8 {
  margin-inline-start: 66.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-9 {
  flex: none;
  width: 75%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-9 {
  margin-inline-start: 75%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-10 {
  flex: none;
  width: 83.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-10 {
  margin-inline-start: 83.3333333333%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-11 {
  flex: none;
  width: 91.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-11 {
  margin-inline-start: 91.6666666667%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-12 {
  flex: none;
  width: 100%;
}
.bulma-columns.bulma-is-mobile > .bulma-column.bulma-is-offset-12 {
  margin-inline-start: 100%;
}
@media screen and (max-width: 768px) {
  .bulma-column.bulma-is-narrow-mobile {
    flex: none;
    width: unset;
  }
  .bulma-column.bulma-is-full-mobile {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-three-quarters-mobile {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-two-thirds-mobile {
    flex: none;
    width: 66.6666%;
  }
  .bulma-column.bulma-is-half-mobile {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-one-third-mobile {
    flex: none;
    width: 33.3333%;
  }
  .bulma-column.bulma-is-one-quarter-mobile {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-one-fifth-mobile {
    flex: none;
    width: 20%;
  }
  .bulma-column.bulma-is-two-fifths-mobile {
    flex: none;
    width: 40%;
  }
  .bulma-column.bulma-is-three-fifths-mobile {
    flex: none;
    width: 60%;
  }
  .bulma-column.bulma-is-four-fifths-mobile {
    flex: none;
    width: 80%;
  }
  .bulma-column.bulma-is-offset-three-quarters-mobile {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-offset-two-thirds-mobile {
    margin-inline-start: 66.6666%;
  }
  .bulma-column.bulma-is-offset-half-mobile {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-offset-one-third-mobile {
    margin-inline-start: 0.3333%;
  }
  .bulma-column.bulma-is-offset-one-quarter-mobile {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-offset-one-fifth-mobile {
    margin-inline-start: 20%;
  }
  .bulma-column.bulma-is-offset-two-fifths-mobile {
    margin-inline-start: 40%;
  }
  .bulma-column.bulma-is-offset-three-fifths-mobile {
    margin-inline-start: 60%;
  }
  .bulma-column.bulma-is-offset-four-fifths-mobile {
    margin-inline-start: 80%;
  }
  .bulma-column.bulma-is-0-mobile {
    flex: none;
    width: 0%;
  }
  .bulma-column.bulma-is-offset-0-mobile {
    margin-inline-start: 0%;
  }
  .bulma-column.bulma-is-1-mobile {
    flex: none;
    width: 8.3333333333%;
  }
  .bulma-column.bulma-is-offset-1-mobile {
    margin-inline-start: 8.3333333333%;
  }
  .bulma-column.bulma-is-2-mobile {
    flex: none;
    width: 16.6666666667%;
  }
  .bulma-column.bulma-is-offset-2-mobile {
    margin-inline-start: 16.6666666667%;
  }
  .bulma-column.bulma-is-3-mobile {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-offset-3-mobile {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-4-mobile {
    flex: none;
    width: 33.3333333333%;
  }
  .bulma-column.bulma-is-offset-4-mobile {
    margin-inline-start: 33.3333333333%;
  }
  .bulma-column.bulma-is-5-mobile {
    flex: none;
    width: 41.6666666667%;
  }
  .bulma-column.bulma-is-offset-5-mobile {
    margin-inline-start: 41.6666666667%;
  }
  .bulma-column.bulma-is-6-mobile {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-offset-6-mobile {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-7-mobile {
    flex: none;
    width: 58.3333333333%;
  }
  .bulma-column.bulma-is-offset-7-mobile {
    margin-inline-start: 58.3333333333%;
  }
  .bulma-column.bulma-is-8-mobile {
    flex: none;
    width: 66.6666666667%;
  }
  .bulma-column.bulma-is-offset-8-mobile {
    margin-inline-start: 66.6666666667%;
  }
  .bulma-column.bulma-is-9-mobile {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-offset-9-mobile {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-10-mobile {
    flex: none;
    width: 83.3333333333%;
  }
  .bulma-column.bulma-is-offset-10-mobile {
    margin-inline-start: 83.3333333333%;
  }
  .bulma-column.bulma-is-11-mobile {
    flex: none;
    width: 91.6666666667%;
  }
  .bulma-column.bulma-is-offset-11-mobile {
    margin-inline-start: 91.6666666667%;
  }
  .bulma-column.bulma-is-12-mobile {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-offset-12-mobile {
    margin-inline-start: 100%;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-column.bulma-is-narrow, .bulma-column.bulma-is-narrow-tablet {
    flex: none;
    width: unset;
  }
  .bulma-column.bulma-is-full, .bulma-column.bulma-is-full-tablet {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-three-quarters, .bulma-column.bulma-is-three-quarters-tablet {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-two-thirds, .bulma-column.bulma-is-two-thirds-tablet {
    flex: none;
    width: 66.6666%;
  }
  .bulma-column.bulma-is-half, .bulma-column.bulma-is-half-tablet {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-one-third, .bulma-column.bulma-is-one-third-tablet {
    flex: none;
    width: 33.3333%;
  }
  .bulma-column.bulma-is-one-quarter, .bulma-column.bulma-is-one-quarter-tablet {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-one-fifth, .bulma-column.bulma-is-one-fifth-tablet {
    flex: none;
    width: 20%;
  }
  .bulma-column.bulma-is-two-fifths, .bulma-column.bulma-is-two-fifths-tablet {
    flex: none;
    width: 40%;
  }
  .bulma-column.bulma-is-three-fifths, .bulma-column.bulma-is-three-fifths-tablet {
    flex: none;
    width: 60%;
  }
  .bulma-column.bulma-is-four-fifths, .bulma-column.bulma-is-four-fifths-tablet {
    flex: none;
    width: 80%;
  }
  .bulma-column.bulma-is-offset-three-quarters, .bulma-column.bulma-is-offset-three-quarters-tablet {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-offset-two-thirds, .bulma-column.bulma-is-offset-two-thirds-tablet {
    margin-inline-start: 66.6666%;
  }
  .bulma-column.bulma-is-offset-half, .bulma-column.bulma-is-offset-half-tablet {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-offset-one-third, .bulma-column.bulma-is-offset-one-third-tablet {
    margin-inline-start: 0.3333%;
  }
  .bulma-column.bulma-is-offset-one-quarter, .bulma-column.bulma-is-offset-one-quarter-tablet {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-offset-one-fifth, .bulma-column.bulma-is-offset-one-fifth-tablet {
    margin-inline-start: 20%;
  }
  .bulma-column.bulma-is-offset-two-fifths, .bulma-column.bulma-is-offset-two-fifths-tablet {
    margin-inline-start: 40%;
  }
  .bulma-column.bulma-is-offset-three-fifths, .bulma-column.bulma-is-offset-three-fifths-tablet {
    margin-inline-start: 60%;
  }
  .bulma-column.bulma-is-offset-four-fifths, .bulma-column.bulma-is-offset-four-fifths-tablet {
    margin-inline-start: 80%;
  }
  .bulma-column.bulma-is-0, .bulma-column.bulma-is-0-tablet {
    flex: none;
    width: 0%;
  }
  .bulma-column.bulma-is-offset-0, .bulma-column.bulma-is-offset-0-tablet {
    margin-inline-start: 0%;
  }
  .bulma-column.bulma-is-1, .bulma-column.bulma-is-1-tablet {
    flex: none;
    width: 8.3333333333%;
  }
  .bulma-column.bulma-is-offset-1, .bulma-column.bulma-is-offset-1-tablet {
    margin-inline-start: 8.3333333333%;
  }
  .bulma-column.bulma-is-2, .bulma-column.bulma-is-2-tablet {
    flex: none;
    width: 16.6666666667%;
  }
  .bulma-column.bulma-is-offset-2, .bulma-column.bulma-is-offset-2-tablet {
    margin-inline-start: 16.6666666667%;
  }
  .bulma-column.bulma-is-3, .bulma-column.bulma-is-3-tablet {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-offset-3, .bulma-column.bulma-is-offset-3-tablet {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-4, .bulma-column.bulma-is-4-tablet {
    flex: none;
    width: 33.3333333333%;
  }
  .bulma-column.bulma-is-offset-4, .bulma-column.bulma-is-offset-4-tablet {
    margin-inline-start: 33.3333333333%;
  }
  .bulma-column.bulma-is-5, .bulma-column.bulma-is-5-tablet {
    flex: none;
    width: 41.6666666667%;
  }
  .bulma-column.bulma-is-offset-5, .bulma-column.bulma-is-offset-5-tablet {
    margin-inline-start: 41.6666666667%;
  }
  .bulma-column.bulma-is-6, .bulma-column.bulma-is-6-tablet {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-offset-6, .bulma-column.bulma-is-offset-6-tablet {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-7, .bulma-column.bulma-is-7-tablet {
    flex: none;
    width: 58.3333333333%;
  }
  .bulma-column.bulma-is-offset-7, .bulma-column.bulma-is-offset-7-tablet {
    margin-inline-start: 58.3333333333%;
  }
  .bulma-column.bulma-is-8, .bulma-column.bulma-is-8-tablet {
    flex: none;
    width: 66.6666666667%;
  }
  .bulma-column.bulma-is-offset-8, .bulma-column.bulma-is-offset-8-tablet {
    margin-inline-start: 66.6666666667%;
  }
  .bulma-column.bulma-is-9, .bulma-column.bulma-is-9-tablet {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-offset-9, .bulma-column.bulma-is-offset-9-tablet {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-10, .bulma-column.bulma-is-10-tablet {
    flex: none;
    width: 83.3333333333%;
  }
  .bulma-column.bulma-is-offset-10, .bulma-column.bulma-is-offset-10-tablet {
    margin-inline-start: 83.3333333333%;
  }
  .bulma-column.bulma-is-11, .bulma-column.bulma-is-11-tablet {
    flex: none;
    width: 91.6666666667%;
  }
  .bulma-column.bulma-is-offset-11, .bulma-column.bulma-is-offset-11-tablet {
    margin-inline-start: 91.6666666667%;
  }
  .bulma-column.bulma-is-12, .bulma-column.bulma-is-12-tablet {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-offset-12, .bulma-column.bulma-is-offset-12-tablet {
    margin-inline-start: 100%;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-column.bulma-is-narrow-touch {
    flex: none;
    width: unset;
  }
  .bulma-column.bulma-is-full-touch {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-three-quarters-touch {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-two-thirds-touch {
    flex: none;
    width: 66.6666%;
  }
  .bulma-column.bulma-is-half-touch {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-one-third-touch {
    flex: none;
    width: 33.3333%;
  }
  .bulma-column.bulma-is-one-quarter-touch {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-one-fifth-touch {
    flex: none;
    width: 20%;
  }
  .bulma-column.bulma-is-two-fifths-touch {
    flex: none;
    width: 40%;
  }
  .bulma-column.bulma-is-three-fifths-touch {
    flex: none;
    width: 60%;
  }
  .bulma-column.bulma-is-four-fifths-touch {
    flex: none;
    width: 80%;
  }
  .bulma-column.bulma-is-offset-three-quarters-touch {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-offset-two-thirds-touch {
    margin-inline-start: 66.6666%;
  }
  .bulma-column.bulma-is-offset-half-touch {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-offset-one-third-touch {
    margin-inline-start: 0.3333%;
  }
  .bulma-column.bulma-is-offset-one-quarter-touch {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-offset-one-fifth-touch {
    margin-inline-start: 20%;
  }
  .bulma-column.bulma-is-offset-two-fifths-touch {
    margin-inline-start: 40%;
  }
  .bulma-column.bulma-is-offset-three-fifths-touch {
    margin-inline-start: 60%;
  }
  .bulma-column.bulma-is-offset-four-fifths-touch {
    margin-inline-start: 80%;
  }
  .bulma-column.bulma-is-0-touch {
    flex: none;
    width: 0%;
  }
  .bulma-column.bulma-is-offset-0-touch {
    margin-inline-start: 0%;
  }
  .bulma-column.bulma-is-1-touch {
    flex: none;
    width: 8.3333333333%;
  }
  .bulma-column.bulma-is-offset-1-touch {
    margin-inline-start: 8.3333333333%;
  }
  .bulma-column.bulma-is-2-touch {
    flex: none;
    width: 16.6666666667%;
  }
  .bulma-column.bulma-is-offset-2-touch {
    margin-inline-start: 16.6666666667%;
  }
  .bulma-column.bulma-is-3-touch {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-offset-3-touch {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-4-touch {
    flex: none;
    width: 33.3333333333%;
  }
  .bulma-column.bulma-is-offset-4-touch {
    margin-inline-start: 33.3333333333%;
  }
  .bulma-column.bulma-is-5-touch {
    flex: none;
    width: 41.6666666667%;
  }
  .bulma-column.bulma-is-offset-5-touch {
    margin-inline-start: 41.6666666667%;
  }
  .bulma-column.bulma-is-6-touch {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-offset-6-touch {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-7-touch {
    flex: none;
    width: 58.3333333333%;
  }
  .bulma-column.bulma-is-offset-7-touch {
    margin-inline-start: 58.3333333333%;
  }
  .bulma-column.bulma-is-8-touch {
    flex: none;
    width: 66.6666666667%;
  }
  .bulma-column.bulma-is-offset-8-touch {
    margin-inline-start: 66.6666666667%;
  }
  .bulma-column.bulma-is-9-touch {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-offset-9-touch {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-10-touch {
    flex: none;
    width: 83.3333333333%;
  }
  .bulma-column.bulma-is-offset-10-touch {
    margin-inline-start: 83.3333333333%;
  }
  .bulma-column.bulma-is-11-touch {
    flex: none;
    width: 91.6666666667%;
  }
  .bulma-column.bulma-is-offset-11-touch {
    margin-inline-start: 91.6666666667%;
  }
  .bulma-column.bulma-is-12-touch {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-offset-12-touch {
    margin-inline-start: 100%;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-column.bulma-is-narrow-desktop {
    flex: none;
    width: unset;
  }
  .bulma-column.bulma-is-full-desktop {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-three-quarters-desktop {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-two-thirds-desktop {
    flex: none;
    width: 66.6666%;
  }
  .bulma-column.bulma-is-half-desktop {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-one-third-desktop {
    flex: none;
    width: 33.3333%;
  }
  .bulma-column.bulma-is-one-quarter-desktop {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-one-fifth-desktop {
    flex: none;
    width: 20%;
  }
  .bulma-column.bulma-is-two-fifths-desktop {
    flex: none;
    width: 40%;
  }
  .bulma-column.bulma-is-three-fifths-desktop {
    flex: none;
    width: 60%;
  }
  .bulma-column.bulma-is-four-fifths-desktop {
    flex: none;
    width: 80%;
  }
  .bulma-column.bulma-is-offset-three-quarters-desktop {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-offset-two-thirds-desktop {
    margin-inline-start: 66.6666%;
  }
  .bulma-column.bulma-is-offset-half-desktop {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-offset-one-third-desktop {
    margin-inline-start: 0.3333%;
  }
  .bulma-column.bulma-is-offset-one-quarter-desktop {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-offset-one-fifth-desktop {
    margin-inline-start: 20%;
  }
  .bulma-column.bulma-is-offset-two-fifths-desktop {
    margin-inline-start: 40%;
  }
  .bulma-column.bulma-is-offset-three-fifths-desktop {
    margin-inline-start: 60%;
  }
  .bulma-column.bulma-is-offset-four-fifths-desktop {
    margin-inline-start: 80%;
  }
  .bulma-column.bulma-is-0-desktop {
    flex: none;
    width: 0%;
  }
  .bulma-column.bulma-is-offset-0-desktop {
    margin-inline-start: 0%;
  }
  .bulma-column.bulma-is-1-desktop {
    flex: none;
    width: 8.3333333333%;
  }
  .bulma-column.bulma-is-offset-1-desktop {
    margin-inline-start: 8.3333333333%;
  }
  .bulma-column.bulma-is-2-desktop {
    flex: none;
    width: 16.6666666667%;
  }
  .bulma-column.bulma-is-offset-2-desktop {
    margin-inline-start: 16.6666666667%;
  }
  .bulma-column.bulma-is-3-desktop {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-offset-3-desktop {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-4-desktop {
    flex: none;
    width: 33.3333333333%;
  }
  .bulma-column.bulma-is-offset-4-desktop {
    margin-inline-start: 33.3333333333%;
  }
  .bulma-column.bulma-is-5-desktop {
    flex: none;
    width: 41.6666666667%;
  }
  .bulma-column.bulma-is-offset-5-desktop {
    margin-inline-start: 41.6666666667%;
  }
  .bulma-column.bulma-is-6-desktop {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-offset-6-desktop {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-7-desktop {
    flex: none;
    width: 58.3333333333%;
  }
  .bulma-column.bulma-is-offset-7-desktop {
    margin-inline-start: 58.3333333333%;
  }
  .bulma-column.bulma-is-8-desktop {
    flex: none;
    width: 66.6666666667%;
  }
  .bulma-column.bulma-is-offset-8-desktop {
    margin-inline-start: 66.6666666667%;
  }
  .bulma-column.bulma-is-9-desktop {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-offset-9-desktop {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-10-desktop {
    flex: none;
    width: 83.3333333333%;
  }
  .bulma-column.bulma-is-offset-10-desktop {
    margin-inline-start: 83.3333333333%;
  }
  .bulma-column.bulma-is-11-desktop {
    flex: none;
    width: 91.6666666667%;
  }
  .bulma-column.bulma-is-offset-11-desktop {
    margin-inline-start: 91.6666666667%;
  }
  .bulma-column.bulma-is-12-desktop {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-offset-12-desktop {
    margin-inline-start: 100%;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-column.bulma-is-narrow-widescreen {
    flex: none;
    width: unset;
  }
  .bulma-column.bulma-is-full-widescreen {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-three-quarters-widescreen {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-two-thirds-widescreen {
    flex: none;
    width: 66.6666%;
  }
  .bulma-column.bulma-is-half-widescreen {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-one-third-widescreen {
    flex: none;
    width: 33.3333%;
  }
  .bulma-column.bulma-is-one-quarter-widescreen {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-one-fifth-widescreen {
    flex: none;
    width: 20%;
  }
  .bulma-column.bulma-is-two-fifths-widescreen {
    flex: none;
    width: 40%;
  }
  .bulma-column.bulma-is-three-fifths-widescreen {
    flex: none;
    width: 60%;
  }
  .bulma-column.bulma-is-four-fifths-widescreen {
    flex: none;
    width: 80%;
  }
  .bulma-column.bulma-is-offset-three-quarters-widescreen {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-offset-two-thirds-widescreen {
    margin-inline-start: 66.6666%;
  }
  .bulma-column.bulma-is-offset-half-widescreen {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-offset-one-third-widescreen {
    margin-inline-start: 0.3333%;
  }
  .bulma-column.bulma-is-offset-one-quarter-widescreen {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-offset-one-fifth-widescreen {
    margin-inline-start: 20%;
  }
  .bulma-column.bulma-is-offset-two-fifths-widescreen {
    margin-inline-start: 40%;
  }
  .bulma-column.bulma-is-offset-three-fifths-widescreen {
    margin-inline-start: 60%;
  }
  .bulma-column.bulma-is-offset-four-fifths-widescreen {
    margin-inline-start: 80%;
  }
  .bulma-column.bulma-is-0-widescreen {
    flex: none;
    width: 0%;
  }
  .bulma-column.bulma-is-offset-0-widescreen {
    margin-inline-start: 0%;
  }
  .bulma-column.bulma-is-1-widescreen {
    flex: none;
    width: 8.3333333333%;
  }
  .bulma-column.bulma-is-offset-1-widescreen {
    margin-inline-start: 8.3333333333%;
  }
  .bulma-column.bulma-is-2-widescreen {
    flex: none;
    width: 16.6666666667%;
  }
  .bulma-column.bulma-is-offset-2-widescreen {
    margin-inline-start: 16.6666666667%;
  }
  .bulma-column.bulma-is-3-widescreen {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-offset-3-widescreen {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-4-widescreen {
    flex: none;
    width: 33.3333333333%;
  }
  .bulma-column.bulma-is-offset-4-widescreen {
    margin-inline-start: 33.3333333333%;
  }
  .bulma-column.bulma-is-5-widescreen {
    flex: none;
    width: 41.6666666667%;
  }
  .bulma-column.bulma-is-offset-5-widescreen {
    margin-inline-start: 41.6666666667%;
  }
  .bulma-column.bulma-is-6-widescreen {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-offset-6-widescreen {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-7-widescreen {
    flex: none;
    width: 58.3333333333%;
  }
  .bulma-column.bulma-is-offset-7-widescreen {
    margin-inline-start: 58.3333333333%;
  }
  .bulma-column.bulma-is-8-widescreen {
    flex: none;
    width: 66.6666666667%;
  }
  .bulma-column.bulma-is-offset-8-widescreen {
    margin-inline-start: 66.6666666667%;
  }
  .bulma-column.bulma-is-9-widescreen {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-offset-9-widescreen {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-10-widescreen {
    flex: none;
    width: 83.3333333333%;
  }
  .bulma-column.bulma-is-offset-10-widescreen {
    margin-inline-start: 83.3333333333%;
  }
  .bulma-column.bulma-is-11-widescreen {
    flex: none;
    width: 91.6666666667%;
  }
  .bulma-column.bulma-is-offset-11-widescreen {
    margin-inline-start: 91.6666666667%;
  }
  .bulma-column.bulma-is-12-widescreen {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-offset-12-widescreen {
    margin-inline-start: 100%;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-column.bulma-is-narrow-fullhd {
    flex: none;
    width: unset;
  }
  .bulma-column.bulma-is-full-fullhd {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-three-quarters-fullhd {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-two-thirds-fullhd {
    flex: none;
    width: 66.6666%;
  }
  .bulma-column.bulma-is-half-fullhd {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-one-third-fullhd {
    flex: none;
    width: 33.3333%;
  }
  .bulma-column.bulma-is-one-quarter-fullhd {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-one-fifth-fullhd {
    flex: none;
    width: 20%;
  }
  .bulma-column.bulma-is-two-fifths-fullhd {
    flex: none;
    width: 40%;
  }
  .bulma-column.bulma-is-three-fifths-fullhd {
    flex: none;
    width: 60%;
  }
  .bulma-column.bulma-is-four-fifths-fullhd {
    flex: none;
    width: 80%;
  }
  .bulma-column.bulma-is-offset-three-quarters-fullhd {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-offset-two-thirds-fullhd {
    margin-inline-start: 66.6666%;
  }
  .bulma-column.bulma-is-offset-half-fullhd {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-offset-one-third-fullhd {
    margin-inline-start: 33.3333%;
  }
  .bulma-column.bulma-is-offset-one-quarter-fullhd {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-offset-one-fifth-fullhd {
    margin-inline-start: 20%;
  }
  .bulma-column.bulma-is-offset-two-fifths-fullhd {
    margin-inline-start: 40%;
  }
  .bulma-column.bulma-is-offset-three-fifths-fullhd {
    margin-inline-start: 60%;
  }
  .bulma-column.bulma-is-offset-four-fifths-fullhd {
    margin-inline-start: 80%;
  }
  .bulma-column.bulma-is-0-fullhd {
    flex: none;
    width: 0%;
  }
  .bulma-column.bulma-is-offset-0-fullhd {
    margin-inline-start: 0%;
  }
  .bulma-column.bulma-is-1-fullhd {
    flex: none;
    width: 8.3333333333%;
  }
  .bulma-column.bulma-is-offset-1-fullhd {
    margin-inline-start: 8.3333333333%;
  }
  .bulma-column.bulma-is-2-fullhd {
    flex: none;
    width: 16.6666666667%;
  }
  .bulma-column.bulma-is-offset-2-fullhd {
    margin-inline-start: 16.6666666667%;
  }
  .bulma-column.bulma-is-3-fullhd {
    flex: none;
    width: 25%;
  }
  .bulma-column.bulma-is-offset-3-fullhd {
    margin-inline-start: 25%;
  }
  .bulma-column.bulma-is-4-fullhd {
    flex: none;
    width: 33.3333333333%;
  }
  .bulma-column.bulma-is-offset-4-fullhd {
    margin-inline-start: 33.3333333333%;
  }
  .bulma-column.bulma-is-5-fullhd {
    flex: none;
    width: 41.6666666667%;
  }
  .bulma-column.bulma-is-offset-5-fullhd {
    margin-inline-start: 41.6666666667%;
  }
  .bulma-column.bulma-is-6-fullhd {
    flex: none;
    width: 50%;
  }
  .bulma-column.bulma-is-offset-6-fullhd {
    margin-inline-start: 50%;
  }
  .bulma-column.bulma-is-7-fullhd {
    flex: none;
    width: 58.3333333333%;
  }
  .bulma-column.bulma-is-offset-7-fullhd {
    margin-inline-start: 58.3333333333%;
  }
  .bulma-column.bulma-is-8-fullhd {
    flex: none;
    width: 66.6666666667%;
  }
  .bulma-column.bulma-is-offset-8-fullhd {
    margin-inline-start: 66.6666666667%;
  }
  .bulma-column.bulma-is-9-fullhd {
    flex: none;
    width: 75%;
  }
  .bulma-column.bulma-is-offset-9-fullhd {
    margin-inline-start: 75%;
  }
  .bulma-column.bulma-is-10-fullhd {
    flex: none;
    width: 83.3333333333%;
  }
  .bulma-column.bulma-is-offset-10-fullhd {
    margin-inline-start: 83.3333333333%;
  }
  .bulma-column.bulma-is-11-fullhd {
    flex: none;
    width: 91.6666666667%;
  }
  .bulma-column.bulma-is-offset-11-fullhd {
    margin-inline-start: 91.6666666667%;
  }
  .bulma-column.bulma-is-12-fullhd {
    flex: none;
    width: 100%;
  }
  .bulma-column.bulma-is-offset-12-fullhd {
    margin-inline-start: 100%;
  }
}

.bulma-columns {
  margin-inline-start: calc(-1 * var(--bulma-column-gap));
  margin-inline-end: calc(-1 * var(--bulma-column-gap));
  margin-top: calc(-1 * var(--bulma-column-gap));
}
.bulma-columns:last-child {
  margin-bottom: calc(-1 * var(--bulma-column-gap));
}
.bulma-columns:not(:last-child) {
  margin-bottom: calc(var(--bulma-block-spacing) - var(--bulma-column-gap));
}
.bulma-columns.bulma-is-centered {
  justify-content: center;
}
.bulma-columns.bulma-is-gapless {
  margin-inline-start: 0;
  margin-inline-end: 0;
  margin-top: 0;
}
.bulma-columns.bulma-is-gapless > .bulma-column {
  margin: 0;
  padding: 0 !important;
}
.bulma-columns.bulma-is-gapless:not(:last-child) {
  margin-bottom: 1.5rem;
}
.bulma-columns.bulma-is-gapless:last-child {
  margin-bottom: 0;
}
.bulma-columns.bulma-is-mobile {
  display: flex;
}
.bulma-columns.bulma-is-multiline {
  flex-wrap: wrap;
}
.bulma-columns.bulma-is-vcentered {
  align-items: center;
}
@media screen and (min-width: 769px), print {
  .bulma-columns:not(.bulma-is-desktop) {
    display: flex;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-desktop {
    display: flex;
  }
}
.bulma-columns.bulma-is-0 {
  --bulma-column-gap: 0rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-0-mobile {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-0-tablet {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-0-tablet-only {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-0-touch {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-0-desktop {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-0-desktop-only {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-0-widescreen {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-0-widescreen-only {
    --bulma-column-gap: 0rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-0-fullhd {
    --bulma-column-gap: 0rem;
  }
}
.bulma-columns.bulma-is-1 {
  --bulma-column-gap: 0.25rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-1-mobile {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-1-tablet {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-1-tablet-only {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-1-touch {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-1-desktop {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-1-desktop-only {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-1-widescreen {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-1-widescreen-only {
    --bulma-column-gap: 0.25rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-1-fullhd {
    --bulma-column-gap: 0.25rem;
  }
}
.bulma-columns.bulma-is-2 {
  --bulma-column-gap: 0.5rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-2-mobile {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-2-tablet {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-2-tablet-only {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-2-touch {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-2-desktop {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-2-desktop-only {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-2-widescreen {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-2-widescreen-only {
    --bulma-column-gap: 0.5rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-2-fullhd {
    --bulma-column-gap: 0.5rem;
  }
}
.bulma-columns.bulma-is-3 {
  --bulma-column-gap: 0.75rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-3-mobile {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-3-tablet {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-3-tablet-only {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-3-touch {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-3-desktop {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-3-desktop-only {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-3-widescreen {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-3-widescreen-only {
    --bulma-column-gap: 0.75rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-3-fullhd {
    --bulma-column-gap: 0.75rem;
  }
}
.bulma-columns.bulma-is-4 {
  --bulma-column-gap: 1rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-4-mobile {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-4-tablet {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-4-tablet-only {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-4-touch {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-4-desktop {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-4-desktop-only {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-4-widescreen {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-4-widescreen-only {
    --bulma-column-gap: 1rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-4-fullhd {
    --bulma-column-gap: 1rem;
  }
}
.bulma-columns.bulma-is-5 {
  --bulma-column-gap: 1.25rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-5-mobile {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-5-tablet {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-5-tablet-only {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-5-touch {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-5-desktop {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-5-desktop-only {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-5-widescreen {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-5-widescreen-only {
    --bulma-column-gap: 1.25rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-5-fullhd {
    --bulma-column-gap: 1.25rem;
  }
}
.bulma-columns.bulma-is-6 {
  --bulma-column-gap: 1.5rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-6-mobile {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-6-tablet {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-6-tablet-only {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-6-touch {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-6-desktop {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-6-desktop-only {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-6-widescreen {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-6-widescreen-only {
    --bulma-column-gap: 1.5rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-6-fullhd {
    --bulma-column-gap: 1.5rem;
  }
}
.bulma-columns.bulma-is-7 {
  --bulma-column-gap: 1.75rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-7-mobile {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-7-tablet {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-7-tablet-only {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-7-touch {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-7-desktop {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-7-desktop-only {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-7-widescreen {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-7-widescreen-only {
    --bulma-column-gap: 1.75rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-7-fullhd {
    --bulma-column-gap: 1.75rem;
  }
}
.bulma-columns.bulma-is-8 {
  --bulma-column-gap: 2rem;
}
@media screen and (max-width: 768px) {
  .bulma-columns.bulma-is-8-mobile {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-columns.bulma-is-8-tablet {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-columns.bulma-is-8-tablet-only {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (max-width: 1023px) {
  .bulma-columns.bulma-is-8-touch {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-columns.bulma-is-8-desktop {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-columns.bulma-is-8-desktop-only {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-columns.bulma-is-8-widescreen {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-columns.bulma-is-8-widescreen-only {
    --bulma-column-gap: 2rem;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-columns.bulma-is-8-fullhd {
    --bulma-column-gap: 2rem;
  }
}

.bulma-fixed-grid {
  container-name: bulma-fixed-grid;
  container-type: inline-size;
}
.bulma-fixed-grid > .bulma-grid {
  --bulma-grid-gap-count: calc(var(--bulma-grid-column-count) - 1);
  --bulma-grid-column-count: 2;
  grid-template-columns: repeat(var(--bulma-grid-column-count), 1fr);
}
.bulma-fixed-grid.bulma-has-1-cols > .bulma-grid {
  --bulma-grid-column-count: 1;
}
.bulma-fixed-grid.bulma-has-2-cols > .bulma-grid {
  --bulma-grid-column-count: 2;
}
.bulma-fixed-grid.bulma-has-3-cols > .bulma-grid {
  --bulma-grid-column-count: 3;
}
.bulma-fixed-grid.bulma-has-4-cols > .bulma-grid {
  --bulma-grid-column-count: 4;
}
.bulma-fixed-grid.bulma-has-5-cols > .bulma-grid {
  --bulma-grid-column-count: 5;
}
.bulma-fixed-grid.bulma-has-6-cols > .bulma-grid {
  --bulma-grid-column-count: 6;
}
.bulma-fixed-grid.bulma-has-7-cols > .bulma-grid {
  --bulma-grid-column-count: 7;
}
.bulma-fixed-grid.bulma-has-8-cols > .bulma-grid {
  --bulma-grid-column-count: 8;
}
.bulma-fixed-grid.bulma-has-9-cols > .bulma-grid {
  --bulma-grid-column-count: 9;
}
.bulma-fixed-grid.bulma-has-10-cols > .bulma-grid {
  --bulma-grid-column-count: 10;
}
.bulma-fixed-grid.bulma-has-11-cols > .bulma-grid {
  --bulma-grid-column-count: 11;
}
.bulma-fixed-grid.bulma-has-12-cols > .bulma-grid {
  --bulma-grid-column-count: 12;
}
@container bulma-fixed-grid (max-width: 768px) {
  .bulma-fixed-grid.bulma-has-1-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 1;
  }
  .bulma-fixed-grid.bulma-has-2-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 2;
  }
  .bulma-fixed-grid.bulma-has-3-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 3;
  }
  .bulma-fixed-grid.bulma-has-4-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 4;
  }
  .bulma-fixed-grid.bulma-has-5-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 5;
  }
  .bulma-fixed-grid.bulma-has-6-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 6;
  }
  .bulma-fixed-grid.bulma-has-7-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 7;
  }
  .bulma-fixed-grid.bulma-has-8-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 8;
  }
  .bulma-fixed-grid.bulma-has-9-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 9;
  }
  .bulma-fixed-grid.bulma-has-10-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 10;
  }
  .bulma-fixed-grid.bulma-has-11-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 11;
  }
  .bulma-fixed-grid.bulma-has-12-cols-mobile > .bulma-grid {
    --bulma-grid-column-count: 12;
  }
}
@container bulma-fixed-grid (min-width: 769px) {
  .bulma-fixed-grid.bulma-has-1-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 1;
  }
  .bulma-fixed-grid.bulma-has-2-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 2;
  }
  .bulma-fixed-grid.bulma-has-3-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 3;
  }
  .bulma-fixed-grid.bulma-has-4-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 4;
  }
  .bulma-fixed-grid.bulma-has-5-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 5;
  }
  .bulma-fixed-grid.bulma-has-6-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 6;
  }
  .bulma-fixed-grid.bulma-has-7-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 7;
  }
  .bulma-fixed-grid.bulma-has-8-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 8;
  }
  .bulma-fixed-grid.bulma-has-9-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 9;
  }
  .bulma-fixed-grid.bulma-has-10-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 10;
  }
  .bulma-fixed-grid.bulma-has-11-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 11;
  }
  .bulma-fixed-grid.bulma-has-12-cols-tablet > .bulma-grid {
    --bulma-grid-column-count: 12;
  }
}
@container bulma-fixed-grid (min-width: 1024px) {
  .bulma-fixed-grid.bulma-has-1-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 1;
  }
  .bulma-fixed-grid.bulma-has-2-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 2;
  }
  .bulma-fixed-grid.bulma-has-3-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 3;
  }
  .bulma-fixed-grid.bulma-has-4-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 4;
  }
  .bulma-fixed-grid.bulma-has-5-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 5;
  }
  .bulma-fixed-grid.bulma-has-6-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 6;
  }
  .bulma-fixed-grid.bulma-has-7-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 7;
  }
  .bulma-fixed-grid.bulma-has-8-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 8;
  }
  .bulma-fixed-grid.bulma-has-9-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 9;
  }
  .bulma-fixed-grid.bulma-has-10-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 10;
  }
  .bulma-fixed-grid.bulma-has-11-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 11;
  }
  .bulma-fixed-grid.bulma-has-12-cols-desktop > .bulma-grid {
    --bulma-grid-column-count: 12;
  }
}
@container bulma-fixed-grid (min-width: 1216px) {
  .bulma-fixed-grid.bulma-has-1-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 1;
  }
  .bulma-fixed-grid.bulma-has-2-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 2;
  }
  .bulma-fixed-grid.bulma-has-3-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 3;
  }
  .bulma-fixed-grid.bulma-has-4-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 4;
  }
  .bulma-fixed-grid.bulma-has-5-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 5;
  }
  .bulma-fixed-grid.bulma-has-6-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 6;
  }
  .bulma-fixed-grid.bulma-has-7-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 7;
  }
  .bulma-fixed-grid.bulma-has-8-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 8;
  }
  .bulma-fixed-grid.bulma-has-9-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 9;
  }
  .bulma-fixed-grid.bulma-has-10-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 10;
  }
  .bulma-fixed-grid.bulma-has-11-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 11;
  }
  .bulma-fixed-grid.bulma-has-12-cols-widescreen > .bulma-grid {
    --bulma-grid-column-count: 12;
  }
}
@container bulma-fixed-grid (min-width: 1408px) {
  .bulma-fixed-grid.bulma-has-1-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 1;
  }
  .bulma-fixed-grid.bulma-has-2-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 2;
  }
  .bulma-fixed-grid.bulma-has-3-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 3;
  }
  .bulma-fixed-grid.bulma-has-4-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 4;
  }
  .bulma-fixed-grid.bulma-has-5-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 5;
  }
  .bulma-fixed-grid.bulma-has-6-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 6;
  }
  .bulma-fixed-grid.bulma-has-7-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 7;
  }
  .bulma-fixed-grid.bulma-has-8-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 8;
  }
  .bulma-fixed-grid.bulma-has-9-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 9;
  }
  .bulma-fixed-grid.bulma-has-10-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 10;
  }
  .bulma-fixed-grid.bulma-has-11-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 11;
  }
  .bulma-fixed-grid.bulma-has-12-cols-fullhd > .bulma-grid {
    --bulma-grid-column-count: 12;
  }
}
@container bulma-fixed-grid (max-width: 768px) {
  .bulma-fixed-grid.bulma-has-auto-count .bulma-grid {
    --bulma-grid-column-count: 2;
  }
}
@container bulma-fixed-grid (min-width: 769px) {
  .bulma-fixed-grid.bulma-has-auto-count .bulma-grid {
    --bulma-grid-column-count: 4;
  }
}
@container bulma-fixed-grid (min-width: 1024px) {
  .bulma-fixed-grid.bulma-has-auto-count .bulma-grid {
    --bulma-grid-column-count: 8;
  }
}
@container bulma-fixed-grid (min-width: 1216px) {
  .bulma-fixed-grid.bulma-has-auto-count .bulma-grid {
    --bulma-grid-column-count: 12;
  }
}
@container bulma-fixed-grid (min-width: 1408px) {
  .bulma-fixed-grid.bulma-has-auto-count .bulma-grid {
    --bulma-grid-column-count: 16;
  }
}

.bulma-grid {
  --bulma-grid-gap: 0.75rem;
  --bulma-grid-column-min: 9rem;
  --bulma-grid-cell-column-span: 1;
  --bulma-grid-cell-row-span: 1;
  display: grid;
  gap: var(--bulma-grid-gap);
  column-gap: var(--bulma-grid-column-gap, var(--bulma-grid-gap));
  row-gap: var(--bulma-grid-row-gap, var(--bulma-grid-gap));
  grid-template-columns: repeat(auto-fit, minmax(var(--bulma-grid-column-min), 1fr));
  grid-template-rows: auto;
}
.bulma-grid.is-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(var(--bulma-grid-column-min), 1fr));
}
.bulma-grid.bulma-is-col-min-1 {
  --bulma-grid-column-min: 1.5rem;
}
.bulma-grid.bulma-is-col-min-2 {
  --bulma-grid-column-min: 3rem;
}
.bulma-grid.bulma-is-col-min-3 {
  --bulma-grid-column-min: 4.5rem;
}
.bulma-grid.bulma-is-col-min-4 {
  --bulma-grid-column-min: 6rem;
}
.bulma-grid.bulma-is-col-min-5 {
  --bulma-grid-column-min: 7.5rem;
}
.bulma-grid.bulma-is-col-min-6 {
  --bulma-grid-column-min: 9rem;
}
.bulma-grid.bulma-is-col-min-7 {
  --bulma-grid-column-min: 10.5rem;
}
.bulma-grid.bulma-is-col-min-8 {
  --bulma-grid-column-min: 12rem;
}
.bulma-grid.bulma-is-col-min-9 {
  --bulma-grid-column-min: 13.5rem;
}
.bulma-grid.bulma-is-col-min-10 {
  --bulma-grid-column-min: 15rem;
}
.bulma-grid.bulma-is-col-min-11 {
  --bulma-grid-column-min: 16.5rem;
}
.bulma-grid.bulma-is-col-min-12 {
  --bulma-grid-column-min: 18rem;
}
.bulma-grid.bulma-is-col-min-13 {
  --bulma-grid-column-min: 19.5rem;
}
.bulma-grid.bulma-is-col-min-14 {
  --bulma-grid-column-min: 21rem;
}
.bulma-grid.bulma-is-col-min-15 {
  --bulma-grid-column-min: 22.5rem;
}
.bulma-grid.bulma-is-col-min-16 {
  --bulma-grid-column-min: 24rem;
}
.bulma-grid.bulma-is-col-min-17 {
  --bulma-grid-column-min: 25.5rem;
}
.bulma-grid.bulma-is-col-min-18 {
  --bulma-grid-column-min: 27rem;
}
.bulma-grid.bulma-is-col-min-19 {
  --bulma-grid-column-min: 28.5rem;
}
.bulma-grid.bulma-is-col-min-20 {
  --bulma-grid-column-min: 30rem;
}
.bulma-grid.bulma-is-col-min-21 {
  --bulma-grid-column-min: 31.5rem;
}
.bulma-grid.bulma-is-col-min-22 {
  --bulma-grid-column-min: 33rem;
}
.bulma-grid.bulma-is-col-min-23 {
  --bulma-grid-column-min: 34.5rem;
}
.bulma-grid.bulma-is-col-min-24 {
  --bulma-grid-column-min: 36rem;
}
.bulma-grid.bulma-is-col-min-25 {
  --bulma-grid-column-min: 37.5rem;
}
.bulma-grid.bulma-is-col-min-26 {
  --bulma-grid-column-min: 39rem;
}
.bulma-grid.bulma-is-col-min-27 {
  --bulma-grid-column-min: 40.5rem;
}
.bulma-grid.bulma-is-col-min-28 {
  --bulma-grid-column-min: 42rem;
}
.bulma-grid.bulma-is-col-min-29 {
  --bulma-grid-column-min: 43.5rem;
}
.bulma-grid.bulma-is-col-min-30 {
  --bulma-grid-column-min: 45rem;
}
.bulma-grid.bulma-is-col-min-31 {
  --bulma-grid-column-min: 46.5rem;
}
.bulma-grid.bulma-is-col-min-32 {
  --bulma-grid-column-min: 48rem;
}

.bulma-cell {
  grid-column-end: span var(--bulma-grid-cell-column-span);
  grid-column-start: var(--bulma-grid-cell-column-start);
  grid-row-end: span var(--bulma-grid-cell-row-span);
  grid-row-start: var(--bulma-grid-cell-row-start);
}
.bulma-cell.bulma-is-col-start-end {
  --bulma-grid-cell-column-start: -1;
}
.bulma-cell.bulma-is-row-start-end {
  --bulma-grid-cell-row-start: -1;
}
.bulma-cell.bulma-is-col-start-1 {
  --bulma-grid-cell-column-start: 1;
}
.bulma-cell.bulma-is-col-end-1 {
  --bulma-grid-cell-column-end: 1;
}
.bulma-cell.bulma-is-col-from-end-1 {
  --bulma-grid-cell-column-start: -1;
}
.bulma-cell.bulma-is-col-span-1 {
  --bulma-grid-cell-column-span: 1;
}
.bulma-cell.bulma-is-row-start-1 {
  --bulma-grid-cell-row-start: 1;
}
.bulma-cell.bulma-is-row-end-1 {
  --bulma-grid-cell-row-end: 1;
}
.bulma-cell.bulma-is-row-from-end-1 {
  --bulma-grid-cell-row-start: -1;
}
.bulma-cell.bulma-is-row-span-1 {
  --bulma-grid-cell-row-span: 1;
}
.bulma-cell.bulma-is-col-start-2 {
  --bulma-grid-cell-column-start: 2;
}
.bulma-cell.bulma-is-col-end-2 {
  --bulma-grid-cell-column-end: 2;
}
.bulma-cell.bulma-is-col-from-end-2 {
  --bulma-grid-cell-column-start: -2;
}
.bulma-cell.bulma-is-col-span-2 {
  --bulma-grid-cell-column-span: 2;
}
.bulma-cell.bulma-is-row-start-2 {
  --bulma-grid-cell-row-start: 2;
}
.bulma-cell.bulma-is-row-end-2 {
  --bulma-grid-cell-row-end: 2;
}
.bulma-cell.bulma-is-row-from-end-2 {
  --bulma-grid-cell-row-start: -2;
}
.bulma-cell.bulma-is-row-span-2 {
  --bulma-grid-cell-row-span: 2;
}
.bulma-cell.bulma-is-col-start-3 {
  --bulma-grid-cell-column-start: 3;
}
.bulma-cell.bulma-is-col-end-3 {
  --bulma-grid-cell-column-end: 3;
}
.bulma-cell.bulma-is-col-from-end-3 {
  --bulma-grid-cell-column-start: -3;
}
.bulma-cell.bulma-is-col-span-3 {
  --bulma-grid-cell-column-span: 3;
}
.bulma-cell.bulma-is-row-start-3 {
  --bulma-grid-cell-row-start: 3;
}
.bulma-cell.bulma-is-row-end-3 {
  --bulma-grid-cell-row-end: 3;
}
.bulma-cell.bulma-is-row-from-end-3 {
  --bulma-grid-cell-row-start: -3;
}
.bulma-cell.bulma-is-row-span-3 {
  --bulma-grid-cell-row-span: 3;
}
.bulma-cell.bulma-is-col-start-4 {
  --bulma-grid-cell-column-start: 4;
}
.bulma-cell.bulma-is-col-end-4 {
  --bulma-grid-cell-column-end: 4;
}
.bulma-cell.bulma-is-col-from-end-4 {
  --bulma-grid-cell-column-start: -4;
}
.bulma-cell.bulma-is-col-span-4 {
  --bulma-grid-cell-column-span: 4;
}
.bulma-cell.bulma-is-row-start-4 {
  --bulma-grid-cell-row-start: 4;
}
.bulma-cell.bulma-is-row-end-4 {
  --bulma-grid-cell-row-end: 4;
}
.bulma-cell.bulma-is-row-from-end-4 {
  --bulma-grid-cell-row-start: -4;
}
.bulma-cell.bulma-is-row-span-4 {
  --bulma-grid-cell-row-span: 4;
}
.bulma-cell.bulma-is-col-start-5 {
  --bulma-grid-cell-column-start: 5;
}
.bulma-cell.bulma-is-col-end-5 {
  --bulma-grid-cell-column-end: 5;
}
.bulma-cell.bulma-is-col-from-end-5 {
  --bulma-grid-cell-column-start: -5;
}
.bulma-cell.bulma-is-col-span-5 {
  --bulma-grid-cell-column-span: 5;
}
.bulma-cell.bulma-is-row-start-5 {
  --bulma-grid-cell-row-start: 5;
}
.bulma-cell.bulma-is-row-end-5 {
  --bulma-grid-cell-row-end: 5;
}
.bulma-cell.bulma-is-row-from-end-5 {
  --bulma-grid-cell-row-start: -5;
}
.bulma-cell.bulma-is-row-span-5 {
  --bulma-grid-cell-row-span: 5;
}
.bulma-cell.bulma-is-col-start-6 {
  --bulma-grid-cell-column-start: 6;
}
.bulma-cell.bulma-is-col-end-6 {
  --bulma-grid-cell-column-end: 6;
}
.bulma-cell.bulma-is-col-from-end-6 {
  --bulma-grid-cell-column-start: -6;
}
.bulma-cell.bulma-is-col-span-6 {
  --bulma-grid-cell-column-span: 6;
}
.bulma-cell.bulma-is-row-start-6 {
  --bulma-grid-cell-row-start: 6;
}
.bulma-cell.bulma-is-row-end-6 {
  --bulma-grid-cell-row-end: 6;
}
.bulma-cell.bulma-is-row-from-end-6 {
  --bulma-grid-cell-row-start: -6;
}
.bulma-cell.bulma-is-row-span-6 {
  --bulma-grid-cell-row-span: 6;
}
.bulma-cell.bulma-is-col-start-7 {
  --bulma-grid-cell-column-start: 7;
}
.bulma-cell.bulma-is-col-end-7 {
  --bulma-grid-cell-column-end: 7;
}
.bulma-cell.bulma-is-col-from-end-7 {
  --bulma-grid-cell-column-start: -7;
}
.bulma-cell.bulma-is-col-span-7 {
  --bulma-grid-cell-column-span: 7;
}
.bulma-cell.bulma-is-row-start-7 {
  --bulma-grid-cell-row-start: 7;
}
.bulma-cell.bulma-is-row-end-7 {
  --bulma-grid-cell-row-end: 7;
}
.bulma-cell.bulma-is-row-from-end-7 {
  --bulma-grid-cell-row-start: -7;
}
.bulma-cell.bulma-is-row-span-7 {
  --bulma-grid-cell-row-span: 7;
}
.bulma-cell.bulma-is-col-start-8 {
  --bulma-grid-cell-column-start: 8;
}
.bulma-cell.bulma-is-col-end-8 {
  --bulma-grid-cell-column-end: 8;
}
.bulma-cell.bulma-is-col-from-end-8 {
  --bulma-grid-cell-column-start: -8;
}
.bulma-cell.bulma-is-col-span-8 {
  --bulma-grid-cell-column-span: 8;
}
.bulma-cell.bulma-is-row-start-8 {
  --bulma-grid-cell-row-start: 8;
}
.bulma-cell.bulma-is-row-end-8 {
  --bulma-grid-cell-row-end: 8;
}
.bulma-cell.bulma-is-row-from-end-8 {
  --bulma-grid-cell-row-start: -8;
}
.bulma-cell.bulma-is-row-span-8 {
  --bulma-grid-cell-row-span: 8;
}
.bulma-cell.bulma-is-col-start-9 {
  --bulma-grid-cell-column-start: 9;
}
.bulma-cell.bulma-is-col-end-9 {
  --bulma-grid-cell-column-end: 9;
}
.bulma-cell.bulma-is-col-from-end-9 {
  --bulma-grid-cell-column-start: -9;
}
.bulma-cell.bulma-is-col-span-9 {
  --bulma-grid-cell-column-span: 9;
}
.bulma-cell.bulma-is-row-start-9 {
  --bulma-grid-cell-row-start: 9;
}
.bulma-cell.bulma-is-row-end-9 {
  --bulma-grid-cell-row-end: 9;
}
.bulma-cell.bulma-is-row-from-end-9 {
  --bulma-grid-cell-row-start: -9;
}
.bulma-cell.bulma-is-row-span-9 {
  --bulma-grid-cell-row-span: 9;
}
.bulma-cell.bulma-is-col-start-10 {
  --bulma-grid-cell-column-start: 10;
}
.bulma-cell.bulma-is-col-end-10 {
  --bulma-grid-cell-column-end: 10;
}
.bulma-cell.bulma-is-col-from-end-10 {
  --bulma-grid-cell-column-start: -10;
}
.bulma-cell.bulma-is-col-span-10 {
  --bulma-grid-cell-column-span: 10;
}
.bulma-cell.bulma-is-row-start-10 {
  --bulma-grid-cell-row-start: 10;
}
.bulma-cell.bulma-is-row-end-10 {
  --bulma-grid-cell-row-end: 10;
}
.bulma-cell.bulma-is-row-from-end-10 {
  --bulma-grid-cell-row-start: -10;
}
.bulma-cell.bulma-is-row-span-10 {
  --bulma-grid-cell-row-span: 10;
}
.bulma-cell.bulma-is-col-start-11 {
  --bulma-grid-cell-column-start: 11;
}
.bulma-cell.bulma-is-col-end-11 {
  --bulma-grid-cell-column-end: 11;
}
.bulma-cell.bulma-is-col-from-end-11 {
  --bulma-grid-cell-column-start: -11;
}
.bulma-cell.bulma-is-col-span-11 {
  --bulma-grid-cell-column-span: 11;
}
.bulma-cell.bulma-is-row-start-11 {
  --bulma-grid-cell-row-start: 11;
}
.bulma-cell.bulma-is-row-end-11 {
  --bulma-grid-cell-row-end: 11;
}
.bulma-cell.bulma-is-row-from-end-11 {
  --bulma-grid-cell-row-start: -11;
}
.bulma-cell.bulma-is-row-span-11 {
  --bulma-grid-cell-row-span: 11;
}
.bulma-cell.bulma-is-col-start-12 {
  --bulma-grid-cell-column-start: 12;
}
.bulma-cell.bulma-is-col-end-12 {
  --bulma-grid-cell-column-end: 12;
}
.bulma-cell.bulma-is-col-from-end-12 {
  --bulma-grid-cell-column-start: -12;
}
.bulma-cell.bulma-is-col-span-12 {
  --bulma-grid-cell-column-span: 12;
}
.bulma-cell.bulma-is-row-start-12 {
  --bulma-grid-cell-row-start: 12;
}
.bulma-cell.bulma-is-row-end-12 {
  --bulma-grid-cell-row-end: 12;
}
.bulma-cell.bulma-is-row-from-end-12 {
  --bulma-grid-cell-row-start: -12;
}
.bulma-cell.bulma-is-row-span-12 {
  --bulma-grid-cell-row-span: 12;
}
@media screen and (max-width: 768px) {
  .bulma-cell.bulma-is-col-start-1-mobile {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-mobile {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-mobile {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-mobile {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-mobile {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-mobile {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-mobile {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-mobile {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-mobile {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-mobile {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-mobile {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-mobile {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-mobile {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-mobile {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-mobile {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-mobile {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-mobile {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-mobile {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-mobile {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-mobile {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-mobile {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-mobile {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-mobile {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-mobile {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-mobile {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-mobile {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-mobile {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-mobile {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-mobile {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-mobile {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-mobile {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-mobile {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-mobile {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-mobile {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-mobile {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-mobile {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-mobile {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-mobile {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-mobile {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-mobile {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-mobile {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-mobile {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-mobile {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-mobile {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-mobile {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-mobile {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-mobile {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-mobile {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-mobile {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-mobile {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-mobile {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-mobile {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-mobile {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-mobile {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-mobile {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-mobile {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-mobile {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-mobile {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-mobile {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-mobile {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-mobile {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-mobile {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-mobile {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-mobile {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-mobile {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-mobile {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-mobile {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-mobile {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-mobile {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-mobile {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-mobile {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-mobile {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-mobile {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-mobile {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-mobile {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-mobile {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-mobile {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-mobile {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-mobile {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-mobile {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-mobile {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-mobile {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-mobile {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-mobile {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-mobile {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-mobile {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-mobile {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-mobile {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-mobile {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-mobile {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-mobile {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-mobile {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-mobile {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-mobile {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-mobile {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-mobile {
    --bulma-grid-cell-row-span: 12;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-cell.bulma-is-col-start-1-tablet {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-tablet {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-tablet {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-tablet {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-tablet {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-tablet {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-tablet {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-tablet {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-tablet {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-tablet {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-tablet {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-tablet {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-tablet {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-tablet {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-tablet {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-tablet {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-tablet {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-tablet {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-tablet {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-tablet {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-tablet {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-tablet {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-tablet {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-tablet {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-tablet {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-tablet {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-tablet {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-tablet {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-tablet {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-tablet {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-tablet {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-tablet {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-tablet {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-tablet {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-tablet {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-tablet {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-tablet {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-tablet {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-tablet {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-tablet {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-tablet {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-tablet {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-tablet {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-tablet {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-tablet {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-tablet {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-tablet {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-tablet {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-tablet {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-tablet {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-tablet {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-tablet {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-tablet {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-tablet {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-tablet {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-tablet {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-tablet {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-tablet {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-tablet {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-tablet {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-tablet {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-tablet {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-tablet {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-tablet {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-tablet {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-tablet {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-tablet {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-tablet {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-tablet {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-tablet {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-tablet {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-tablet {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-tablet {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-tablet {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-tablet {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-tablet {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-tablet {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-tablet {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-tablet {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-tablet {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-tablet {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-tablet {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-tablet {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-tablet {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-tablet {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-tablet {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-tablet {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-tablet {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-tablet {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-tablet {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-tablet {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-tablet {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-tablet {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-tablet {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-tablet {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-tablet {
    --bulma-grid-cell-row-span: 12;
  }
}
@media screen and (min-width: 769px) and (max-width: 1023px) {
  .bulma-cell.bulma-is-col-start-1-tablet-only {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-tablet-only {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-tablet-only {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-tablet-only {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-tablet-only {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-tablet-only {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-tablet-only {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-tablet-only {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-tablet-only {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-tablet-only {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-tablet-only {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-tablet-only {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-tablet-only {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-tablet-only {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-tablet-only {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-tablet-only {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-tablet-only {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-tablet-only {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-tablet-only {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-tablet-only {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-tablet-only {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-tablet-only {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-tablet-only {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-tablet-only {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-tablet-only {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-tablet-only {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-tablet-only {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-tablet-only {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-tablet-only {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-tablet-only {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-tablet-only {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-tablet-only {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-tablet-only {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-tablet-only {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-tablet-only {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-tablet-only {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-tablet-only {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-tablet-only {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-tablet-only {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-tablet-only {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-tablet-only {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-tablet-only {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-tablet-only {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-tablet-only {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-tablet-only {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-tablet-only {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-tablet-only {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-tablet-only {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-tablet-only {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-tablet-only {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-tablet-only {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-tablet-only {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-tablet-only {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-tablet-only {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-tablet-only {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-tablet-only {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-tablet-only {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-tablet-only {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-tablet-only {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-tablet-only {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-tablet-only {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-tablet-only {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-tablet-only {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-tablet-only {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-tablet-only {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-tablet-only {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-tablet-only {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-tablet-only {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-tablet-only {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-tablet-only {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-tablet-only {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-tablet-only {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-tablet-only {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-tablet-only {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-tablet-only {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-tablet-only {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-tablet-only {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-tablet-only {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-tablet-only {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-tablet-only {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-tablet-only {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-tablet-only {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-tablet-only {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-tablet-only {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-tablet-only {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-tablet-only {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-tablet-only {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-tablet-only {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-tablet-only {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-tablet-only {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-tablet-only {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-tablet-only {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-tablet-only {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-tablet-only {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-tablet-only {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-tablet-only {
    --bulma-grid-cell-row-span: 12;
  }
}
@media screen and (min-width: 1024px) {
  .bulma-cell.bulma-is-col-start-1-desktop {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-desktop {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-desktop {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-desktop {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-desktop {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-desktop {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-desktop {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-desktop {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-desktop {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-desktop {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-desktop {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-desktop {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-desktop {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-desktop {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-desktop {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-desktop {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-desktop {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-desktop {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-desktop {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-desktop {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-desktop {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-desktop {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-desktop {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-desktop {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-desktop {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-desktop {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-desktop {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-desktop {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-desktop {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-desktop {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-desktop {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-desktop {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-desktop {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-desktop {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-desktop {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-desktop {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-desktop {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-desktop {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-desktop {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-desktop {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-desktop {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-desktop {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-desktop {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-desktop {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-desktop {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-desktop {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-desktop {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-desktop {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-desktop {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-desktop {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-desktop {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-desktop {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-desktop {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-desktop {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-desktop {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-desktop {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-desktop {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-desktop {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-desktop {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-desktop {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-desktop {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-desktop {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-desktop {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-desktop {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-desktop {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-desktop {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-desktop {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-desktop {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-desktop {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-desktop {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-desktop {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-desktop {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-desktop {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-desktop {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-desktop {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-desktop {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-desktop {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-desktop {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-desktop {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-desktop {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-desktop {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-desktop {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-desktop {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-desktop {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-desktop {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-desktop {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-desktop {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-desktop {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-desktop {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-desktop {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-desktop {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-desktop {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-desktop {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-desktop {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-desktop {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-desktop {
    --bulma-grid-cell-row-span: 12;
  }
}
@media screen and (min-width: 1024px) and (max-width: 1215px) {
  .bulma-cell.bulma-is-col-start-1-desktop-only {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-desktop-only {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-desktop-only {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-desktop-only {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-desktop-only {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-desktop-only {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-desktop-only {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-desktop-only {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-desktop-only {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-desktop-only {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-desktop-only {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-desktop-only {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-desktop-only {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-desktop-only {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-desktop-only {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-desktop-only {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-desktop-only {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-desktop-only {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-desktop-only {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-desktop-only {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-desktop-only {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-desktop-only {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-desktop-only {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-desktop-only {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-desktop-only {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-desktop-only {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-desktop-only {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-desktop-only {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-desktop-only {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-desktop-only {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-desktop-only {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-desktop-only {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-desktop-only {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-desktop-only {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-desktop-only {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-desktop-only {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-desktop-only {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-desktop-only {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-desktop-only {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-desktop-only {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-desktop-only {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-desktop-only {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-desktop-only {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-desktop-only {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-desktop-only {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-desktop-only {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-desktop-only {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-desktop-only {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-desktop-only {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-desktop-only {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-desktop-only {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-desktop-only {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-desktop-only {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-desktop-only {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-desktop-only {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-desktop-only {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-desktop-only {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-desktop-only {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-desktop-only {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-desktop-only {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-desktop-only {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-desktop-only {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-desktop-only {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-desktop-only {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-desktop-only {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-desktop-only {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-desktop-only {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-desktop-only {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-desktop-only {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-desktop-only {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-desktop-only {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-desktop-only {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-desktop-only {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-desktop-only {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-desktop-only {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-desktop-only {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-desktop-only {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-desktop-only {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-desktop-only {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-desktop-only {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-desktop-only {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-desktop-only {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-desktop-only {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-desktop-only {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-desktop-only {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-desktop-only {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-desktop-only {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-desktop-only {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-desktop-only {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-desktop-only {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-desktop-only {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-desktop-only {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-desktop-only {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-desktop-only {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-desktop-only {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-desktop-only {
    --bulma-grid-cell-row-span: 12;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-cell.bulma-is-col-start-1-widescreen {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-widescreen {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-widescreen {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-widescreen {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-widescreen {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-widescreen {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-widescreen {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-widescreen {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-widescreen {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-widescreen {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-widescreen {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-widescreen {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-widescreen {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-widescreen {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-widescreen {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-widescreen {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-widescreen {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-widescreen {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-widescreen {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-widescreen {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-widescreen {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-widescreen {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-widescreen {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-widescreen {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-widescreen {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-widescreen {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-widescreen {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-widescreen {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-widescreen {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-widescreen {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-widescreen {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-widescreen {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-widescreen {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-widescreen {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-widescreen {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-widescreen {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-widescreen {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-widescreen {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-widescreen {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-widescreen {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-widescreen {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-widescreen {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-widescreen {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-widescreen {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-widescreen {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-widescreen {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-widescreen {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-widescreen {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-widescreen {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-widescreen {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-widescreen {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-widescreen {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-widescreen {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-widescreen {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-widescreen {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-widescreen {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-widescreen {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-widescreen {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-widescreen {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-widescreen {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-widescreen {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-widescreen {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-widescreen {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-widescreen {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-widescreen {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-widescreen {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-widescreen {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-widescreen {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-widescreen {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-widescreen {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-widescreen {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-widescreen {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-widescreen {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-widescreen {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-widescreen {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-widescreen {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-widescreen {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-widescreen {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-widescreen {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-widescreen {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-widescreen {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-widescreen {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-widescreen {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-widescreen {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-widescreen {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-widescreen {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-widescreen {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-widescreen {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-widescreen {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-widescreen {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-widescreen {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-widescreen {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-widescreen {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-widescreen {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-widescreen {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-widescreen {
    --bulma-grid-cell-row-span: 12;
  }
}
@media screen and (min-width: 1216px) and (max-width: 1407px) {
  .bulma-cell.bulma-is-col-start-1-widescreen-only {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-widescreen-only {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-widescreen-only {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-widescreen-only {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-widescreen-only {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-widescreen-only {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-widescreen-only {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-widescreen-only {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-widescreen-only {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-widescreen-only {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-widescreen-only {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-widescreen-only {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-widescreen-only {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-widescreen-only {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-widescreen-only {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-widescreen-only {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-widescreen-only {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-widescreen-only {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-widescreen-only {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-widescreen-only {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-widescreen-only {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-widescreen-only {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-widescreen-only {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-widescreen-only {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-widescreen-only {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-widescreen-only {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-widescreen-only {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-widescreen-only {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-widescreen-only {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-widescreen-only {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-widescreen-only {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-widescreen-only {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-widescreen-only {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-widescreen-only {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-widescreen-only {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-widescreen-only {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-widescreen-only {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-widescreen-only {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-widescreen-only {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-widescreen-only {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-widescreen-only {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-widescreen-only {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-widescreen-only {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-widescreen-only {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-widescreen-only {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-widescreen-only {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-widescreen-only {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-widescreen-only {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-widescreen-only {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-widescreen-only {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-widescreen-only {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-widescreen-only {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-widescreen-only {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-widescreen-only {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-widescreen-only {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-widescreen-only {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-widescreen-only {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-widescreen-only {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-widescreen-only {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-widescreen-only {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-widescreen-only {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-widescreen-only {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-widescreen-only {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-widescreen-only {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-widescreen-only {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-widescreen-only {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-widescreen-only {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-widescreen-only {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-widescreen-only {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-widescreen-only {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-widescreen-only {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-widescreen-only {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-widescreen-only {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-widescreen-only {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-widescreen-only {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-widescreen-only {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-widescreen-only {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-widescreen-only {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-widescreen-only {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-widescreen-only {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-widescreen-only {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-widescreen-only {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-widescreen-only {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-widescreen-only {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-widescreen-only {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-widescreen-only {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-widescreen-only {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-widescreen-only {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-widescreen-only {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-widescreen-only {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-widescreen-only {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-widescreen-only {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-widescreen-only {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-widescreen-only {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-widescreen-only {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-widescreen-only {
    --bulma-grid-cell-row-span: 12;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-cell.bulma-is-col-start-1-fullhd {
    --bulma-grid-cell-column-start: 1;
  }
  .bulma-cell.bulma-is-col-end-1-fullhd {
    --bulma-grid-cell-column-end: 1;
  }
  .bulma-cell.bulma-is-col-from-end-1-fullhd {
    --bulma-grid-cell-column-start: -1;
  }
  .bulma-cell.bulma-is-col-span-1-fullhd {
    --bulma-grid-cell-column-span: 1;
  }
  .bulma-cell.bulma-is-row-start-1-fullhd {
    --bulma-grid-cell-row-start: 1;
  }
  .bulma-cell.bulma-is-row-end-1-fullhd {
    --bulma-grid-cell-row-end: 1;
  }
  .bulma-cell.bulma-is-row-from-end-1-fullhd {
    --bulma-grid-cell-row-start: -1;
  }
  .bulma-cell.bulma-is-row-span-1-fullhd {
    --bulma-grid-cell-row-span: 1;
  }
  .bulma-cell.bulma-is-col-start-2-fullhd {
    --bulma-grid-cell-column-start: 2;
  }
  .bulma-cell.bulma-is-col-end-2-fullhd {
    --bulma-grid-cell-column-end: 2;
  }
  .bulma-cell.bulma-is-col-from-end-2-fullhd {
    --bulma-grid-cell-column-start: -2;
  }
  .bulma-cell.bulma-is-col-span-2-fullhd {
    --bulma-grid-cell-column-span: 2;
  }
  .bulma-cell.bulma-is-row-start-2-fullhd {
    --bulma-grid-cell-row-start: 2;
  }
  .bulma-cell.bulma-is-row-end-2-fullhd {
    --bulma-grid-cell-row-end: 2;
  }
  .bulma-cell.bulma-is-row-from-end-2-fullhd {
    --bulma-grid-cell-row-start: -2;
  }
  .bulma-cell.bulma-is-row-span-2-fullhd {
    --bulma-grid-cell-row-span: 2;
  }
  .bulma-cell.bulma-is-col-start-3-fullhd {
    --bulma-grid-cell-column-start: 3;
  }
  .bulma-cell.bulma-is-col-end-3-fullhd {
    --bulma-grid-cell-column-end: 3;
  }
  .bulma-cell.bulma-is-col-from-end-3-fullhd {
    --bulma-grid-cell-column-start: -3;
  }
  .bulma-cell.bulma-is-col-span-3-fullhd {
    --bulma-grid-cell-column-span: 3;
  }
  .bulma-cell.bulma-is-row-start-3-fullhd {
    --bulma-grid-cell-row-start: 3;
  }
  .bulma-cell.bulma-is-row-end-3-fullhd {
    --bulma-grid-cell-row-end: 3;
  }
  .bulma-cell.bulma-is-row-from-end-3-fullhd {
    --bulma-grid-cell-row-start: -3;
  }
  .bulma-cell.bulma-is-row-span-3-fullhd {
    --bulma-grid-cell-row-span: 3;
  }
  .bulma-cell.bulma-is-col-start-4-fullhd {
    --bulma-grid-cell-column-start: 4;
  }
  .bulma-cell.bulma-is-col-end-4-fullhd {
    --bulma-grid-cell-column-end: 4;
  }
  .bulma-cell.bulma-is-col-from-end-4-fullhd {
    --bulma-grid-cell-column-start: -4;
  }
  .bulma-cell.bulma-is-col-span-4-fullhd {
    --bulma-grid-cell-column-span: 4;
  }
  .bulma-cell.bulma-is-row-start-4-fullhd {
    --bulma-grid-cell-row-start: 4;
  }
  .bulma-cell.bulma-is-row-end-4-fullhd {
    --bulma-grid-cell-row-end: 4;
  }
  .bulma-cell.bulma-is-row-from-end-4-fullhd {
    --bulma-grid-cell-row-start: -4;
  }
  .bulma-cell.bulma-is-row-span-4-fullhd {
    --bulma-grid-cell-row-span: 4;
  }
  .bulma-cell.bulma-is-col-start-5-fullhd {
    --bulma-grid-cell-column-start: 5;
  }
  .bulma-cell.bulma-is-col-end-5-fullhd {
    --bulma-grid-cell-column-end: 5;
  }
  .bulma-cell.bulma-is-col-from-end-5-fullhd {
    --bulma-grid-cell-column-start: -5;
  }
  .bulma-cell.bulma-is-col-span-5-fullhd {
    --bulma-grid-cell-column-span: 5;
  }
  .bulma-cell.bulma-is-row-start-5-fullhd {
    --bulma-grid-cell-row-start: 5;
  }
  .bulma-cell.bulma-is-row-end-5-fullhd {
    --bulma-grid-cell-row-end: 5;
  }
  .bulma-cell.bulma-is-row-from-end-5-fullhd {
    --bulma-grid-cell-row-start: -5;
  }
  .bulma-cell.bulma-is-row-span-5-fullhd {
    --bulma-grid-cell-row-span: 5;
  }
  .bulma-cell.bulma-is-col-start-6-fullhd {
    --bulma-grid-cell-column-start: 6;
  }
  .bulma-cell.bulma-is-col-end-6-fullhd {
    --bulma-grid-cell-column-end: 6;
  }
  .bulma-cell.bulma-is-col-from-end-6-fullhd {
    --bulma-grid-cell-column-start: -6;
  }
  .bulma-cell.bulma-is-col-span-6-fullhd {
    --bulma-grid-cell-column-span: 6;
  }
  .bulma-cell.bulma-is-row-start-6-fullhd {
    --bulma-grid-cell-row-start: 6;
  }
  .bulma-cell.bulma-is-row-end-6-fullhd {
    --bulma-grid-cell-row-end: 6;
  }
  .bulma-cell.bulma-is-row-from-end-6-fullhd {
    --bulma-grid-cell-row-start: -6;
  }
  .bulma-cell.bulma-is-row-span-6-fullhd {
    --bulma-grid-cell-row-span: 6;
  }
  .bulma-cell.bulma-is-col-start-7-fullhd {
    --bulma-grid-cell-column-start: 7;
  }
  .bulma-cell.bulma-is-col-end-7-fullhd {
    --bulma-grid-cell-column-end: 7;
  }
  .bulma-cell.bulma-is-col-from-end-7-fullhd {
    --bulma-grid-cell-column-start: -7;
  }
  .bulma-cell.bulma-is-col-span-7-fullhd {
    --bulma-grid-cell-column-span: 7;
  }
  .bulma-cell.bulma-is-row-start-7-fullhd {
    --bulma-grid-cell-row-start: 7;
  }
  .bulma-cell.bulma-is-row-end-7-fullhd {
    --bulma-grid-cell-row-end: 7;
  }
  .bulma-cell.bulma-is-row-from-end-7-fullhd {
    --bulma-grid-cell-row-start: -7;
  }
  .bulma-cell.bulma-is-row-span-7-fullhd {
    --bulma-grid-cell-row-span: 7;
  }
  .bulma-cell.bulma-is-col-start-8-fullhd {
    --bulma-grid-cell-column-start: 8;
  }
  .bulma-cell.bulma-is-col-end-8-fullhd {
    --bulma-grid-cell-column-end: 8;
  }
  .bulma-cell.bulma-is-col-from-end-8-fullhd {
    --bulma-grid-cell-column-start: -8;
  }
  .bulma-cell.bulma-is-col-span-8-fullhd {
    --bulma-grid-cell-column-span: 8;
  }
  .bulma-cell.bulma-is-row-start-8-fullhd {
    --bulma-grid-cell-row-start: 8;
  }
  .bulma-cell.bulma-is-row-end-8-fullhd {
    --bulma-grid-cell-row-end: 8;
  }
  .bulma-cell.bulma-is-row-from-end-8-fullhd {
    --bulma-grid-cell-row-start: -8;
  }
  .bulma-cell.bulma-is-row-span-8-fullhd {
    --bulma-grid-cell-row-span: 8;
  }
  .bulma-cell.bulma-is-col-start-9-fullhd {
    --bulma-grid-cell-column-start: 9;
  }
  .bulma-cell.bulma-is-col-end-9-fullhd {
    --bulma-grid-cell-column-end: 9;
  }
  .bulma-cell.bulma-is-col-from-end-9-fullhd {
    --bulma-grid-cell-column-start: -9;
  }
  .bulma-cell.bulma-is-col-span-9-fullhd {
    --bulma-grid-cell-column-span: 9;
  }
  .bulma-cell.bulma-is-row-start-9-fullhd {
    --bulma-grid-cell-row-start: 9;
  }
  .bulma-cell.bulma-is-row-end-9-fullhd {
    --bulma-grid-cell-row-end: 9;
  }
  .bulma-cell.bulma-is-row-from-end-9-fullhd {
    --bulma-grid-cell-row-start: -9;
  }
  .bulma-cell.bulma-is-row-span-9-fullhd {
    --bulma-grid-cell-row-span: 9;
  }
  .bulma-cell.bulma-is-col-start-10-fullhd {
    --bulma-grid-cell-column-start: 10;
  }
  .bulma-cell.bulma-is-col-end-10-fullhd {
    --bulma-grid-cell-column-end: 10;
  }
  .bulma-cell.bulma-is-col-from-end-10-fullhd {
    --bulma-grid-cell-column-start: -10;
  }
  .bulma-cell.bulma-is-col-span-10-fullhd {
    --bulma-grid-cell-column-span: 10;
  }
  .bulma-cell.bulma-is-row-start-10-fullhd {
    --bulma-grid-cell-row-start: 10;
  }
  .bulma-cell.bulma-is-row-end-10-fullhd {
    --bulma-grid-cell-row-end: 10;
  }
  .bulma-cell.bulma-is-row-from-end-10-fullhd {
    --bulma-grid-cell-row-start: -10;
  }
  .bulma-cell.bulma-is-row-span-10-fullhd {
    --bulma-grid-cell-row-span: 10;
  }
  .bulma-cell.bulma-is-col-start-11-fullhd {
    --bulma-grid-cell-column-start: 11;
  }
  .bulma-cell.bulma-is-col-end-11-fullhd {
    --bulma-grid-cell-column-end: 11;
  }
  .bulma-cell.bulma-is-col-from-end-11-fullhd {
    --bulma-grid-cell-column-start: -11;
  }
  .bulma-cell.bulma-is-col-span-11-fullhd {
    --bulma-grid-cell-column-span: 11;
  }
  .bulma-cell.bulma-is-row-start-11-fullhd {
    --bulma-grid-cell-row-start: 11;
  }
  .bulma-cell.bulma-is-row-end-11-fullhd {
    --bulma-grid-cell-row-end: 11;
  }
  .bulma-cell.bulma-is-row-from-end-11-fullhd {
    --bulma-grid-cell-row-start: -11;
  }
  .bulma-cell.bulma-is-row-span-11-fullhd {
    --bulma-grid-cell-row-span: 11;
  }
  .bulma-cell.bulma-is-col-start-12-fullhd {
    --bulma-grid-cell-column-start: 12;
  }
  .bulma-cell.bulma-is-col-end-12-fullhd {
    --bulma-grid-cell-column-end: 12;
  }
  .bulma-cell.bulma-is-col-from-end-12-fullhd {
    --bulma-grid-cell-column-start: -12;
  }
  .bulma-cell.bulma-is-col-span-12-fullhd {
    --bulma-grid-cell-column-span: 12;
  }
  .bulma-cell.bulma-is-row-start-12-fullhd {
    --bulma-grid-cell-row-start: 12;
  }
  .bulma-cell.bulma-is-row-end-12-fullhd {
    --bulma-grid-cell-row-end: 12;
  }
  .bulma-cell.bulma-is-row-from-end-12-fullhd {
    --bulma-grid-cell-row-start: -12;
  }
  .bulma-cell.bulma-is-row-span-12-fullhd {
    --bulma-grid-cell-row-span: 12;
  }
}

/* Bulma Components */
.bulma-container {
  flex-grow: 1;
  margin: 0 auto;
  position: relative;
  width: 100%;
}
.bulma-container.bulma-is-fluid {
  max-width: none !important;
  padding-left: 32px;
  padding-right: 32px;
  width: 100%;
}
.bulma-container.bulma-is-max-tablet {
  max-width: 705px;
}
@media screen and (min-width: 1024px) {
  .bulma-container {
    max-width: 960px;
  }
}
@media screen and (max-width: 1215px) {
  .bulma-container.bulma-is-widescreen:not(.bulma-is-max-tablet):not(.bulma-is-max-desktop) {
    max-width: 1152px;
  }
}
@media screen and (max-width: 1407px) {
  .bulma-container.bulma-is-fullhd:not(.bulma-is-max-tablet):not(.bulma-is-max-desktop):not(.bulma-is-max-widescreen) {
    max-width: 1344px;
  }
}
@media screen and (min-width: 1216px) {
  .bulma-container:not(.bulma-is-max-tablet):not(.bulma-is-max-desktop) {
    max-width: 1152px;
  }
}
@media screen and (min-width: 1408px) {
  .bulma-container:not(.bulma-is-max-tablet):not(.bulma-is-max-desktop):not(.bulma-is-max-widescreen) {
    max-width: 1344px;
  }
}

.bulma-footer {
  --bulma-footer-background-color: var(--bulma-scheme-main-bis);
  --bulma-footer-color: false;
  --bulma-footer-padding: 3rem 1.5rem 6rem;
  background-color: var(--bulma-footer-background-color);
  padding: var(--bulma-footer-padding);
}

.bulma-hero {
  --bulma-hero-body-padding: 3rem 1.5rem;
  --bulma-hero-body-padding-tablet: 3rem 3rem;
  --bulma-hero-body-padding-small: 1.5rem;
  --bulma-hero-body-padding-medium: 9rem 4.5rem;
  --bulma-hero-body-padding-large: 18rem 6rem;
}

.bulma-hero {
  align-items: stretch;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.bulma-hero .bulma-navbar {
  background: none;
}
.bulma-hero .bulma-tabs ul {
  border-bottom: none;
}
.bulma-hero.bulma-is-white {
  --bulma-hero-h: var(--bulma-white-h);
  --bulma-hero-s: var(--bulma-white-s);
  --bulma-hero-background-l: var(--bulma-white-l);
  --bulma-hero-color-l: var(--bulma-white-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-white .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-white .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-white .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-white .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-white.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-white.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-black {
  --bulma-hero-h: var(--bulma-black-h);
  --bulma-hero-s: var(--bulma-black-s);
  --bulma-hero-background-l: var(--bulma-black-l);
  --bulma-hero-color-l: var(--bulma-black-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-black .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-black .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-black .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-black .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-black.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-black.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-light {
  --bulma-hero-h: var(--bulma-light-h);
  --bulma-hero-s: var(--bulma-light-s);
  --bulma-hero-background-l: var(--bulma-light-l);
  --bulma-hero-color-l: var(--bulma-light-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-light .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-light .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-light .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-light .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-light.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-light.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-dark {
  --bulma-hero-h: var(--bulma-dark-h);
  --bulma-hero-s: var(--bulma-dark-s);
  --bulma-hero-background-l: var(--bulma-dark-l);
  --bulma-hero-color-l: var(--bulma-dark-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-dark .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-dark .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-dark .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-dark .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-dark.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-dark.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-text {
  --bulma-hero-h: var(--bulma-text-h);
  --bulma-hero-s: var(--bulma-text-s);
  --bulma-hero-background-l: var(--bulma-text-l);
  --bulma-hero-color-l: var(--bulma-text-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-text .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-text .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-text .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-text .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-text.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-text.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-primary {
  --bulma-hero-h: var(--bulma-primary-h);
  --bulma-hero-s: var(--bulma-primary-s);
  --bulma-hero-background-l: var(--bulma-primary-l);
  --bulma-hero-color-l: var(--bulma-primary-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-primary .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-primary .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-primary .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-primary .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-primary.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-primary.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-link {
  --bulma-hero-h: var(--bulma-link-h);
  --bulma-hero-s: var(--bulma-link-s);
  --bulma-hero-background-l: var(--bulma-link-l);
  --bulma-hero-color-l: var(--bulma-link-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-link .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-link .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-link .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-link .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-link.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-link.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-info {
  --bulma-hero-h: var(--bulma-info-h);
  --bulma-hero-s: var(--bulma-info-s);
  --bulma-hero-background-l: var(--bulma-info-l);
  --bulma-hero-color-l: var(--bulma-info-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-info .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-info .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-info .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-info .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-info.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-info.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-success {
  --bulma-hero-h: var(--bulma-success-h);
  --bulma-hero-s: var(--bulma-success-s);
  --bulma-hero-background-l: var(--bulma-success-l);
  --bulma-hero-color-l: var(--bulma-success-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-success .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-success .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-success .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-success .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-success.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-success.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-warning {
  --bulma-hero-h: var(--bulma-warning-h);
  --bulma-hero-s: var(--bulma-warning-s);
  --bulma-hero-background-l: var(--bulma-warning-l);
  --bulma-hero-color-l: var(--bulma-warning-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-warning .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-warning .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-warning .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-warning .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-warning.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-warning.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-danger {
  --bulma-hero-h: var(--bulma-danger-h);
  --bulma-hero-s: var(--bulma-danger-s);
  --bulma-hero-background-l: var(--bulma-danger-l);
  --bulma-hero-color-l: var(--bulma-danger-invert-l);
  background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-danger .bulma-navbar {
  --bulma-navbar-item-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-hover-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-navbar-item-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-navbar-item-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-danger .bulma-tabs {
  --bulma-tabs-link-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-background-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-tabs-boxed-link-active-border-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
  --bulma-tabs-link-active-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l));
}
.bulma-hero.bulma-is-danger .bulma-subtitle {
  --bulma-subtitle-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-subtitle-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-danger .bulma-title {
  --bulma-title-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
  --bulma-title-strong-color: hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l));
}
.bulma-hero.bulma-is-danger.bulma-is-bold {
  background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-background-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
}
@media screen and (max-width: 768px) {
  .bulma-hero.bulma-is-danger.bulma-is-bold .bulma-navbar-menu {
    background-image: linear-gradient(141deg, hsl(calc(var(--bulma-hero-h) - 5deg), calc(var(--bulma-hero-s) + 10%), calc(var(--bulma-hero-background-l) + 5%)) 0%, hsl(var(--bulma-hero-h), var(--bulma-hero-s), var(--bulma-hero-color-l)) 71%, hsl(calc(var(--bulma-hero-h) + 5deg), calc(var(--bulma-hero-s) - 10%), calc(var(--bulma-hero-background-l) - 5%)) 100%);
  }
}
.bulma-hero.bulma-is-small .bulma-hero-body {
  padding: var(--bulma-hero-body-padding-small);
}
@media screen and (min-width: 769px), print {
  .bulma-hero.bulma-is-medium .bulma-hero-body {
    padding: var(--bulma-hero-body-padding-medium);
  }
}
@media screen and (min-width: 769px), print {
  .bulma-hero.bulma-is-large .bulma-hero-body {
    padding: var(--bulma-hero-body-padding-large);
  }
}
.bulma-hero.bulma-is-halfheight .bulma-hero-body, .bulma-hero.bulma-is-fullheight .bulma-hero-body, .bulma-hero.bulma-is-fullheight-with-navbar .bulma-hero-body {
  align-items: center;
  display: flex;
}
.bulma-hero.bulma-is-halfheight .bulma-hero-body > .bulma-container, .bulma-hero.bulma-is-fullheight .bulma-hero-body > .bulma-container, .bulma-hero.bulma-is-fullheight-with-navbar .bulma-hero-body > .bulma-container {
  flex-grow: 1;
  flex-shrink: 1;
}
.bulma-hero.bulma-is-halfheight {
  min-height: 50vh;
}
.bulma-hero.bulma-is-fullheight {
  min-height: 100vh;
}

.bulma-hero-video {
  overflow: hidden;
}
.bulma-hero-video video {
  left: 50%;
  min-height: 100%;
  min-width: 100%;
  position: absolute;
  top: 50%;
  transform: translate3d(-50%, -50%, 0);
}
.bulma-hero-video.bulma-is-transparent {
  opacity: 0.3;
}
@media screen and (max-width: 768px) {
  .bulma-hero-video {
    display: none;
  }
}

.bulma-hero-buttons {
  margin-top: 1.5rem;
}
@media screen and (max-width: 768px) {
  .bulma-hero-buttons .bulma-button {
    display: flex;
  }
  .bulma-hero-buttons .bulma-button:not(:last-child) {
    margin-bottom: 0.75rem;
  }
}
@media screen and (min-width: 769px), print {
  .bulma-hero-buttons {
    display: flex;
    justify-content: center;
  }
  .bulma-hero-buttons .bulma-button:not(:last-child) {
    margin-inline-end: 1.5rem;
  }
}

.bulma-hero-head,
.bulma-hero-foot {
  flex-grow: 0;
  flex-shrink: 0;
}

.bulma-hero-body {
  flex-grow: 1;
  flex-shrink: 0;
  padding: var(--bulma-hero-body-padding);
}
@media screen and (min-width: 769px), print {
  .bulma-hero-body {
    padding: var(--bulma-hero-body-padding-tablet);
  }
}

.bulma-level {
  --bulma-level-item-spacing: calc(var(--bulma-block-spacing) * 0.5);
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: var(--bulma-level-item-spacing);
}
.bulma-level code {
  border-radius: var(--bulma-radius);
}
.bulma-level img {
  display: inline-block;
  vertical-align: top;
}
.bulma-level.bulma-is-mobile {
  display: flex;
  flex-direction: row;
}
.bulma-level.bulma-is-mobile .bulma-level-left,
.bulma-level.bulma-is-mobile .bulma-level-right {
  display: flex;
}
.bulma-level.bulma-is-mobile .bulma-level-item:not(.bulma-is-narrow) {
  flex-grow: 1;
}
@media screen and (min-width: 769px), print {
  .bulma-level {
    display: flex;
    flex-direction: row;
  }
  .bulma-level > .bulma-level-item:not(.bulma-is-narrow) {
    flex-grow: 1;
  }
}

.bulma-level-item {
  align-items: center;
  display: flex;
  flex-basis: auto;
  flex-grow: 0;
  flex-shrink: 0;
  justify-content: center;
}
.bulma-level-item .bulma-title,
.bulma-level-item .bulma-subtitle {
  margin-bottom: 0;
}

.bulma-level-left,
.bulma-level-right {
  flex-basis: auto;
  flex-grow: 0;
  flex-shrink: 0;
  gap: calc(var(--bulma-block-spacing) * 0.5);
}
.bulma-level-left .bulma-level-item.bulma-is-flexible,
.bulma-level-right .bulma-level-item.bulma-is-flexible {
  flex-grow: 1;
}

.bulma-level-left {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
@media screen and (min-width: 769px), print {
  .bulma-level-left {
    flex-direction: row;
  }
}

.bulma-level-right {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
@media screen and (min-width: 769px), print {
  .bulma-level-right {
    flex-direction: row;
  }
}

.bulma-media {
  --bulma-media-border-color: hsla(var(--bulma-scheme-h), var(--bulma-scheme-s), var(--bulma-border-l), 0.5);
  --bulma-media-border-size: 1px;
  --bulma-media-spacing: 1rem;
  --bulma-media-spacing-large: 1.5rem;
  --bulma-media-content-spacing: 0.75rem;
  --bulma-media-level-1-spacing: 0.75rem;
  --bulma-media-level-1-content-spacing: 0.5rem;
  --bulma-media-level-2-spacing: 0.5rem;
  align-items: flex-start;
  display: flex;
  text-align: inherit;
}
.bulma-media .bulma-content:not(:last-child) {
  margin-bottom: var(--bulma-media-content-spacing);
}
.bulma-media .bulma-media {
  border-top-color: var(--bulma-media-border-color);
  border-top-style: solid;
  border-top-width: var(--bulma-media-border-size);
  display: flex;
  padding-top: var(--bulma-media-level-1-spacing);
}
.bulma-media .bulma-media .bulma-content:not(:last-child),
.bulma-media .bulma-media .bulma-control:not(:last-child) {
  margin-bottom: var(--bulma-media-level-1-content-spacing);
}
.bulma-media .bulma-media .bulma-media {
  padding-top: var(--bulma-media-level-2-spacing);
}
.bulma-media .bulma-media .bulma-media + .bulma-media {
  margin-top: var(--bulma-media-level-2-spacing);
}
.bulma-media + .bulma-media {
  border-top-color: var(--bulma-media-border-color);
  border-top-style: solid;
  border-top-width: var(--bulma-media-border-size);
  margin-top: var(--bulma-media-spacing);
  padding-top: var(--bulma-media-spacing);
}
.bulma-media.bulma-is-large + .bulma-media {
  margin-top: var(--bulma-media-spacing-large);
  padding-top: var(--bulma-media-spacing-large);
}

.bulma-media-left,
.bulma-media-right {
  flex-basis: auto;
  flex-grow: 0;
  flex-shrink: 0;
}

.bulma-media-left {
  margin-inline-end: var(--bulma-media-spacing);
}

.bulma-media-right {
  margin-inline-start: var(--bulma-media-spacing);
}

.bulma-media-content {
  flex-basis: auto;
  flex-grow: 1;
  flex-shrink: 1;
  text-align: inherit;
}

@media screen and (max-width: 768px) {
  .bulma-media-content {
    overflow-x: auto;
  }
}
.bulma-section {
  --bulma-section-padding: 3rem 1.5rem;
  --bulma-section-padding-desktop: 3rem 3rem;
  --bulma-section-padding-medium: 9rem 4.5rem;
  --bulma-section-padding-large: 18rem 6rem;
  padding: var(--bulma-section-padding);
}
@media screen and (min-width: 1024px) {
  .bulma-section {
    padding: var(--bulma-section-padding-desktop);
  }
  .bulma-section.bulma-is-medium {
    padding: var(--bulma-section-padding-medium);
  }
  .bulma-section.bulma-is-large {
    padding: var(--bulma-section-padding-large);
  }
}
.bulma-section.bulma-is-fullheight {
  min-height: 100vh;
}

:root {
  --bulma-skeleton-background: var(--bulma-border);
  --bulma-skeleton-radius: var(--bulma-radius-small);
  --bulma-skeleton-block-min-height: 4.5em;
  --bulma-skeleton-lines-gap: 0.75em;
  --bulma-skeleton-line-height: 0.75em;
}

.bulma-skeleton-lines > div, .bulma-skeleton-block, .bulma-has-skeleton::after, .bulma-is-skeleton {
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-name: pulsate;
  animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
  background-color: var(--bulma-skeleton-background);
  border-radius: var(--bulma-skeleton-radius);
  box-shadow: none;
  pointer-events: none;
}

.bulma-is-skeleton {
  color: transparent !important;
}
.bulma-is-skeleton em,
.bulma-is-skeleton strong {
  color: inherit;
}
.bulma-is-skeleton img {
  visibility: hidden;
}
.bulma-is-skeleton.bulma-checkbox input {
  opacity: 0;
}
.bulma-is-skeleton.bulma-delete {
  border-radius: var(--bulma-radius-rounded);
}
.bulma-is-skeleton.bulma-delete::before, .bulma-is-skeleton.bulma-delete::after {
  display: none;
}

input.bulma-is-skeleton,
textarea.bulma-is-skeleton {
  resize: none;
}
input.bulma-is-skeleton::-moz-placeholder,
textarea.bulma-is-skeleton::-moz-placeholder {
  color: transparent !important;
}
input.bulma-is-skeleton::-webkit-input-placeholder,
textarea.bulma-is-skeleton::-webkit-input-placeholder {
  color: transparent !important;
}
input.bulma-is-skeleton:-moz-placeholder,
textarea.bulma-is-skeleton:-moz-placeholder {
  color: transparent !important;
}
input.bulma-is-skeleton:-ms-input-placeholder,
textarea.bulma-is-skeleton:-ms-input-placeholder {
  color: transparent !important;
}

.bulma-has-skeleton {
  color: transparent !important;
  position: relative;
}
.bulma-has-skeleton::after {
  content: "";
  display: block;
  height: 100%;
  left: 0;
  max-width: 100%;
  min-width: 10%;
  position: absolute;
  top: 0;
  width: 7em;
}

.bulma-skeleton-block {
  color: transparent !important;
  min-height: var(--bulma-skeleton-block-min-height);
}

.bulma-skeleton-lines {
  color: transparent !important;
  display: flex;
  flex-direction: column;
  gap: var(--bulma-skeleton-lines-gap);
  position: relative;
}
.bulma-skeleton-lines > div {
  height: var(--bulma-skeleton-line-height);
}
.bulma-skeleton-lines > div:last-child {
  min-width: 4em;
  width: 30%;
}

/*# sourceMappingURL=bulma-no-helpers-prefixed.css.map */
