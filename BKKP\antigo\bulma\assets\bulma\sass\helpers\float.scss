@use "../utilities/initial-variables" as iv;
@use "../utilities/mixins" as mx;

.#{iv.$helpers-prefix}clearfix {
  @include mx.clearfix;
}

.#{iv.$helpers-prefix}float-left,
.#{iv.$helpers-prefix}pulled-left {
  float: left !important;
}

.#{iv.$helpers-prefix}float-right,
.#{iv.$helpers-prefix}pulled-right {
  float: right !important;
}

.#{iv.$helpers-prefix}float-none {
  float: none !important;
}

$clears: both left none right;

@each $clear in $clears {
  .#{iv.$helpers-prefix}clear-#{$clear} {
    clear: $clear !important;
  }
}
